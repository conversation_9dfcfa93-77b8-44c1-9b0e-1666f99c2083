<script setup lang="ts">
import { ConfigProvider } from 'radix-vue'

const useIdFn = () => useId()

useHead({
  title: "Dappgenie Technologies",
})
const { hideLoader, hasVisited } = useLoader()
await preloadComponents([
  'Navbar',
  'Footer',
  'Hero',
  'AboutUs',
  'Services',
  'ServicesBlockchain',
  'TextScrollDiv',
  'Features',
  'HowItWorks',
  'Partners',
  'Industries',
  'Community',
  'Technologies',
  'Testimonials',
  'Team',
  'Contact',
  'FAQ'])
  
// If user has visited before, hide loader immediately
// Otherwise, wait for animation
// Handle loader timing
onMounted(() => {
  if (hasVisited.value) {
    hideLoader()
  } else {
    setTimeout(() => {
      hideLoader()
    }, 3000)
  }
})
</script>

<template>
  <ConfigProvider :use-id="useIdFn">
    <NuxtLoadingIndicator />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </ConfigProvider>
</template>