---
title: 'TypeScript Development First Steps with Bun and Modern Tooling'
description: 'A comprehensive guide to getting started with TypeScript development using Bun runtime, modern linting tools like OXC and Biome, essential VS Code extensions, and optimized development workflows.'
image:
  src: '/images/blogs/4/intro.png'
  alt: 'TypeScript development setup with Bun runtime and modern tooling'
categories:
  - programming
  - web-development
  - typescript
  - tooling
  - bun
authors:
  - name: 'Dappgenie Technologies'
    avatar: '/logo.png'
    social: '0xD7F4'
publishedAt: 2024-01-15
---

# Typescript Development First Steps

Welcome to your first-class ticket into the TypeScript universe—carry-on baggage: curiosity, swagger, and a dash of pragmatism. This guide will get you from zero to "oh wow, this is clean" using <PERSON>un as your runtime, modern linting with OXC or Biome, a tiny-but-mighty type utility, and a VS Code setup that feels like cheating (but isn't).

---

## Why Bun?

Because it's fast, batteries-included, and actually fun to use. Bun is a runtime, package manager, bundler, and test runner rolled into one. Less tool glue, more building.

Quick hits:
- Blazing-fast installs and runs
- First-class TypeScript support
- Built-in test runner, bundler, and more

If you want a great overview of what <PERSON><PERSON> brings to the table, start here: <a href="https://blog.seancoughlin.me/bun-the-comprehensive-toolkit-for-javascript-and-typescript" style="color: #00ddeb; text-decoration: none;">blog.seancoughlin.me</a>. For a simple REST server with `Bun.serve`, check out this walkthrough: <a href="https://dev.to/nazeelashraf/writing-a-simple-restful-typescript-web-service-using-bunjs-f7n" style="color: #00ddeb; text-decoration: none;">dev.to</a>.

Official docs: <a href="https://bun.sh" style="color: #00ddeb; text-decoration: none;">bun.sh</a>

---

## Project Bootstrap with Bun

Spin up a fresh TS project and keep the vibe minimal:

```bash
bun init
bun add -d typescript
```

Run scripts with:
```bash
bun run dev
bun test
```

Bun's comfy defaults mean you'll write less config and more code. If you enjoy production-ready scaffolding with TS + linting and editor integration, this piece is a nice companion read: <a href="https://vdelacou.medium.com/build-a-production-ready-typescript-app-with-bun-eslint-and-vscode-integration-a9c183cc2682" style="color: #00ddeb; text-decoration: none;">vdelacou.medium.com</a>.

---

## The Prettify Helper (Type-Level Clarity)

![](/images/blogs/4/the-prettify-helper.png)

Sometimes TypeScript types can look like a Jackson Pollock painting—beautiful but… chaotic. A small utility known as "Prettify" can make your inferred types readable by flattening and normalizing their structure:

```ts
type Prettify<T> = {
  [K in keyof T]: T[K];
} & {};
```

Use it when:
- You want cleaner hover tooltips in VS Code
- You're debugging a gnarly generic type
- You want to "lock in" a simplified shape for DX

Example:
```ts
type Result = { a: string } & { b: number };
type PrettyResult = Prettify<Result>; // shows as { a: string; b: number }
```

It's not runtime code—it's purely for developer ergonomics. Keep it handy and your future self will high-five you.

---

## Linting: OXC or Biome

TypeScript thrives with good guardrails. Two modern options:

1) OXC (linting-first, performance-focused)
- A next-gen linter designed for speed and modern JS/TS.
- Integrates nicely with editors.
- The ecosystem around OXC is evolving quickly; it focuses on linting and aims for great DX. It's also exploring type-check synergies via external tools.
- Project: <a href="https://oxc.rs" style="color: #00ddeb; text-decoration: none;">oxc.rs</a>

2) Biome (linter + formatter)
- All-in-one: lints, formats, and speeds up your workflow with a single tool.
- Great defaults, strong perf, minimal config.
- Project: <a href="https://biomejs.dev" style="color: #00ddeb; text-decoration: none;">biomejs.dev</a>

Note on formatting: Biome ships a formatter today. The OXC project has indicated they plan to launch their own formatter, which will give you more flexibility if you want to stay in a single ecosystem.

Pick one:
- Want a "single binary" that lints and formats now? Choose Biome.
- Want to bet on a fast linter and keep formatting flexible? Choose OXC (and pair it with your formatter of choice today).

For a broader take on modern linting/formatting stacks and editor integration, see this write-up: <a href="https://onwebfocus.com/bun" style="color: #00ddeb; text-decoration: none;">onwebfocus.com</a>.

---

## Editor Setup: VS Code Extensions You'll Actually Use

These aren't fluff—they directly sharpen your feedback loop and code quality.

### Error Lens

Error visibility that doesn't suck: <a href="https://marketplace.visualstudio.com/items?itemName=usernamehw.errorlens" style="color: #00ddeb; text-decoration: none;">Error Lens</a> inlines diagnostics directly in your code instead of making you hunt through the Problems panel. Instant visibility means instant fixes, and your debugging speed will thank you.

### Pretty TypeScript Errors

TypeScript errors made human: Ever stared at a cryptic TS error that looked like it was written by aliens? <a href="https://marketplace.visualstudio.com/items?itemName=yoavbls.pretty-ts-errors" style="color: #00ddeb; text-decoration: none;">Pretty TypeScript Errors</a> translates those hieroglyphics into readable, actionable messages. Less time deciphering, more time shipping.

### Type Challenges

Level up your type game: <a href="https://marketplace.visualstudio.com/items?itemName=YRM.type-challenges" style="color: #00ddeb; text-decoration: none;">Type Challenges</a> brings the famous TypeScript challenges right into your editor. Think of it as daily type kung-fu practice—small reps that build stronger types and fewer bugs over time.

### Turbo Console Log

Smart logging without the tedium: <a href="https://marketplace.visualstudio.com/items?itemName=ChakrounAnas.turbo-console-log" style="color: #00ddeb; text-decoration: none;">Turbo Console Log</a> generates contextual console.log statements fast. When you need to trace execution without writing repetitive logs, this saves serious time.

### Better Comments & Comment Anchors

Comments that actually communicate: Two extensions work beautifully together here. <a href="https://marketplace.visualstudio.com/items?itemName=aaron-bond.better-comments" style="color: #00ddeb; text-decoration: none;">Better Comments</a> styles your comments with colors and categories (alerts, todos, highlights), while <a href="https://marketplace.visualstudio.com/items?itemName=ExodiusStudios.comment-anchors" style="color: #00ddeb; text-decoration: none;">Comment Anchors</a> creates navigable anchors like TODO and FIXME across your entire project. Your intentions pop visually, and your team gets a navigable map of work-in-progress.

### Code Spell Checker

Clean copy everywhere: <a href="https://marketplace.visualstudio.com/items?itemName=streetsidesoftware.code-spell-checker" style="color: #00ddeb; text-decoration: none;">Code Spell Checker</a> catches typos in code, comments, and strings. It's low noise but high value—clean copy matters in APIs, docs, and variable names.

Pair these with your linter's VS Code integration for on-save lint/format and you'll have a buttery editing experience. For deeper thoughts on integrating lint/format into editor workflows, check out <a href="https://onwebfocus.com/bun" style="color: #00ddeb; text-decoration: none;">onwebfocus.com</a>.

---

## A Minimal Dev Loop You'll Love

1) bun install  
2) bun run dev (or bun run start)  
3) Lint on-save with OXC or Biome  
4) Test with bun test  
5) Use Prettify to simplify debugging complex TS types

The goal: feedback in seconds, not minutes.

---

## Bonus: TDD with Bun (Optional, but tasty)

Bun includes a test runner, so you can write tests first and code with confidence. If you want a primer on TDD mindset and TypeScript workflows, this article gives a gentle ramp, even if it's not Bun-specific: <a href="https://medium.com/@tiffanyadisuryo/ppl-tiffany-lindy-adisuryo-c4710749d2d0" style="color: #00ddeb; text-decoration: none;">medium.com</a>.

---

![](/images/blogs/4/wrap-it-up.gif)

- Bun keeps your toolchain tight and fast. Docs: [bun.sh](https://bun.sh)
- Use Prettify to make complex types human-friendly.
- Choose OXC or Biome for a modern linting experience. Projects: [oxc.rs](https://oxc.rs), [biomejs.dev](https://biomejs.dev)
- Supercharge VS Code with targeted extensions that reduce friction and boost clarity.

That's it for now; it will continue. You've got the gear—time to build something crisp.
