---
title: 'TypeScript Development First Steps with Bun and Modern Tooling'
description: 'A comprehensive guide to getting started with TypeScript development using Bun runtime, modern linting tools like OXC and Biome, essential VS Code extensions, and optimized development workflows.'
image:
  src: 'https://images.unsplash.com/photo-1581276879432-15e50529f34b?q=80&w=1024&auto=format&fit=crop'
  alt: 'TypeScript code on a computer screen with modern development tools'
categories:
  - programming
  - web-development
  - typescript
  - tooling
  - bun
authors:
  - name: 'Dappgenie Technologies'
    avatar: '/logo.png'
    social: '0xD7F4'
publishedAt: 2024-01-15
---

# Typescript Development First Steps

Welcome to your first-class ticket into the TypeScript universe—carry-on baggage: curiosity, swagger, and a dash of pragmatism. This guide will get you from zero to "oh wow, this is clean" using <PERSON>un as your runtime, modern linting with OXC or Biome, a tiny-but-mighty type utility, and a VS Code setup that feels like cheating (but isn't).

---

## Why Bun?

Because it's fast, batteries-included, and actually fun to use. Bun is a runtime, package manager, bundler, and test runner rolled into one. Less tool glue, more building.

Quick hits:
- Blazing-fast installs and runs
- First-class TypeScript support
- Built-in test runner, bundler, and more

If you want a great overview of what Bun brings to the table, start here: [blog.seancoughlin.me](https://blog.seancoughlin.me/bun-the-comprehensive-toolkit-for-javascript-and-typescript). For a simple REST server with `Bun.serve`, check out this walkthrough: [dev.to](https://dev.to/nazeelashraf/writing-a-simple-restful-typescript-web-service-using-bunjs-f7n).

Official docs: [bun.sh](https://bun.sh)

---

## Project Bootstrap with Bun

Spin up a fresh TS project and keep the vibe minimal:

```bash
bun init
bun add -d typescript
```

Run scripts with:
```bash
bun run dev
bun test
```

Bun's comfy defaults mean you'll write less config and more code. If you enjoy production-ready scaffolding with TS + linting and editor integration, this piece is a nice companion read: [vdelacou.medium.com](https://vdelacou.medium.com/build-a-production-ready-typescript-app-with-bun-eslint-and-vscode-integration-a9c183cc2682).

---

## The Prettify Helper (Type-Level Clarity)

Sometimes TypeScript types can look like a Jackson Pollock painting—beautiful but… chaotic. A small utility known as "Prettify" can make your inferred types readable by flattening and normalizing their structure:

```ts
type Prettify<T> = {
  [K in keyof T]: T[K];
} & {};
```

Use it when:
- You want cleaner hover tooltips in VS Code
- You're debugging a gnarly generic type
- You want to "lock in" a simplified shape for DX

Example:
```ts
type Result = { a: string } & { b: number };
type PrettyResult = Prettify<Result>; // shows as { a: string; b: number }
```

It's not runtime code—it's purely for developer ergonomics. Keep it handy and your future self will high-five you.

---

## Linting: OXC or Biome

TypeScript thrives with good guardrails. Two modern options:

1) OXC (linting-first, performance-focused)
- A next-gen linter designed for speed and modern JS/TS.
- Integrates nicely with editors.
- The ecosystem around OXC is evolving quickly; it focuses on linting and aims for great DX. It's also exploring type-check synergies via external tools.
- Project: [oxc.rs](https://oxc.rs)

2) Biome (linter + formatter)
- All-in-one: lints, formats, and speeds up your workflow with a single tool.
- Great defaults, strong perf, minimal config.
- Project: [biomejs.dev](https://biomejs.dev)

Note on formatting: Biome ships a formatter today. The OXC project has indicated they plan to launch their own formatter, which will give you more flexibility if you want to stay in a single ecosystem.

Pick one:
- Want a "single binary" that lints and formats now? Choose Biome.
- Want to bet on a fast linter and keep formatting flexible? Choose OXC (and pair it with your formatter of choice today).

For a broader take on modern linting/formatting stacks and editor integration, see this write-up: [onwebfocus.com](https://onwebfocus.com/bun).

---

## Editor Setup: VS Code Extensions You'll Actually Use

These aren't fluff—they directly sharpen your feedback loop and code quality.

1) Error Lens  
Link: https://marketplace.visualstudio.com/items?itemName=usernamehw.errorlens  
What it does: Inlines diagnostics (errors, warnings) directly in your code, so you don't have to squint at the Problems panel.  
Why you need it: Instant visibility = instant fixes. Speeds up debugging dramatically.

2) Pretty TypeScript Errors  
Link: https://marketplace.visualstudio.com/items?itemName=yoavbls.pretty-ts-errors  
What it does: Turns cryptic TS errors into readable, actionable messages.  
Why you need it: You'll spend less time deciphering and more time shipping.

3) Type Challenges  
Link: https://marketplace.visualstudio.com/items?itemName=YRM.type-challenges  
What it does: Brings the famous TypeScript type challenges into your editor.  
Why you need it: Level up your type kung-fu in small daily reps. Stronger types = fewer bugs.

4) Turbo Console Log  
Link: https://marketplace.visualstudio.com/items?itemName=ChakrounAnas.turbo-console-log  
What it does: Generates smart, contextual console.log statements fast.  
Why you need it: When you need to trace quickly without writing repetitive logs, this is a time-saver.

5) Better Comments  
Link: https://marketplace.visualstudio.com/items?itemName=aaron-bond.better-comments  
What it does: Styles and categorizes comments (alerts, todos, highlights).  
Why you need it: Makes your intentions pop, and your future self won't hate you.

6) Comment Anchors  
Link: https://marketplace.visualstudio.com/items?itemName=ExodiusStudios.comment-anchors  
What it does: Adds navigable anchors like TODO, FIXME, NOTE across files.  
Why you need it: Create a navigable map of your work-in-progress—great for team visibility.

7) Code Spell Checker  
Link: https://marketplace.visualstudio.com/items?itemName=streetsidesoftware.code-spell-checker  
What it does: Catches common typos in code, comments, and strings.  
Why you need it: Clean copy matters in APIs, docs, and variable names. It's low noise and high value.

Tip: Pair these with your linter's VS Code integration for on-save lint/format and a buttery editing experience. For thoughts on integrating lint/format into editor flows, see: [onwebfocus.com](https://onwebfocus.com/bun).

---

## A Minimal Dev Loop You'll Love

1) bun install  
2) bun run dev (or bun run start)  
3) Lint on-save with OXC or Biome  
4) Test with bun test  
5) Use Prettify to simplify debugging complex TS types

The goal: feedback in seconds, not minutes.

---

## Bonus: TDD with Bun (Optional, but tasty)

Bun includes a test runner, so you can write tests first and code with confidence. If you want a primer on TDD mindset and TypeScript workflows, this article gives a gentle ramp, even if it's not Bun-specific: [medium.com](https://medium.com/@tiffanyadisuryo/ppl-tiffany-lindy-adisuryo-c4710749d2d0).

---

## Wrap-Up

- Bun keeps your toolchain tight and fast. Docs: [bun.sh](https://bun.sh)
- Use Prettify to make complex types human-friendly.
- Choose OXC or Biome for a modern linting experience. Projects: [oxc.rs](https://oxc.rs), [biomejs.dev](https://biomejs.dev)
- Supercharge VS Code with targeted extensions that reduce friction and boost clarity.

That's it for now; it will continue. You've got the gear—time to build something crisp.
