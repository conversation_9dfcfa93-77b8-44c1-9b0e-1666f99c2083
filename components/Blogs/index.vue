<!-- pages/blogs/index.vue -->
<script setup lang="ts">
interface BlogPost {
  _path: string
  title: string
  description: string
  image: {
    src: string
    alt: string
  }
  publishedAt: string
  authors: Array<{
    name: string
    avatar: string
    social: string
  }>
  categories: string[]
}

// Get all blog posts sorted by date
const { data: blogs } = await useAsyncData('blogs', () => 
  queryContent('/blogs')
    .where({ _extension: 'md' })
    .sort({ publishedAt: -1 })
    .find()
)

// Format date to readable string
const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Format category for display
const formatCategory = (category: string) => {
  return category.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}
</script>

<template>
  <section
    id="services"
    class="container py-24 sm:py-32"
  >
    <h2 class="text-lg text-primary text-center mb-2 tracking-wider">
      Our Blogs
    </h2>

    <h2 class="text-3xl md:text-4xl text-center font-bold mb-4">
      Read Dappgenie Blogs
    </h2>
    <h3 class="w-full sm:w-3/4 lg:w-1/2 mx-auto text-lg text-center text-muted-foreground mb-8">
      Insights and updates from the world of blockchain technology, cryptocurrency, and decentralized applications.
    </h3>

      <!-- Blog Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <article 
          v-for="blog in blogs" 
          :key="blog._path"
          class="group bg-card rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 border border-border"
        >
          <NuxtLink :to="blog._path" class="block h-full">
            <!-- Blog Image -->
            <div class="relative aspect-[16/9] overflow-hidden bg-muted">
              <img 
                :src="blog.image?.src" 
                :alt="blog.image?.alt"
                class="object-cover w-full h-full group-hover:scale-105 transition-transform duration-500 ease-out"
              >
              <!-- Categories -->
              <div class="absolute top-4 left-4 flex gap-2 flex-wrap">
                <span 
                  v-for="category in blog.categories" 
                  :key="category"
                  class="text-xs font-medium bg-background/95 backdrop-blur-sm text-foreground px-3 py-1 rounded-full shadow-sm"
                >
                  {{ formatCategory(category) }}
                </span>
              </div>
            </div>

            <!-- Blog Content -->
            <div class="p-6">
              <h2 class="text-lg line-clamp-2 font-semibold mb-3 text-foreground group-hover:text-primary transition-colors duration-300">
                {{ blog.title }}
              </h2>
              <p class="text-muted-foreground mb-6 line-clamp-2 text-sm">
                {{ blog.description }}
              </p>

              <!-- Author and Date -->
              <div class="flex items-center justify-between border-t border-border pt-4">
                <div class="flex items-center space-x-2">
                  <div class="w-8 h-8 rounded-full overflow-hidden bg-muted flex items-center justify-center">
                    <img 
                      v-if="blog.authors?.[0]?.avatar"
                      :src="blog.authors[0].avatar" 
                      :alt="blog.authors[0].name"
                      class="w-full h-full object-cover"
                    >
                    <span v-else class="text-xs text-muted-foreground">
                      {{ blog.authors?.[0]?.name?.charAt(0) }}
                    </span>
                  </div>
                  <span class="text-sm text-muted-foreground font-medium">
                    {{ blog.authors?.[0]?.name }}
                  </span>
                </div>
                <time class="text-sm text-muted-foreground">
                  {{ formatDate(blog.publishedAt) }}
                </time>
              </div>
            </div>
          </NuxtLink>
        </article>
      </div>
  </section>
</template>

<style scoped>
.grid {
  @apply gap-6 lg:gap-8;
}

article {
  @apply transform-gpu transition-all;
}

article:hover {
  @apply -translate-y-1;
}
</style>