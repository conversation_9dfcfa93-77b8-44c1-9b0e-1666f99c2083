<template>
    <figure
      class="relative w-96 cursor-pointer overflow-hidden rounded-xl group/bento hover:shadow-xl  p-4  backdrop-blur-[50px] bg-[rgb(115,115,115)]/[0.07] shadow-[inset_2px_4px_16px_0px_rgba(248,248,248,0.06)]"
    >
      <div class="flex flex-row items-center gap-2">
        <NuxtImg :src="img" class="rounded-full" width="32" height="32" alt="" />
        <div class="flex flex-col">
          <span class="text-sm font-medium">
            {{ name }}
          </span>
          <p class="text-xs font-normal">{{ username }}</p>
        </div>
      </div>
      <blockquote class="mt-2 text-xs text-muted-foreground leading-5" v-html="body"/>
    </figure>
  </template>
  
  <script lang="ts" setup>
  defineProps({
    img: { type: String, required: true },
    name: { type: String, required: true },
    username: { type: String, required: true },
    body: { type: String, required: true },
  });
  </script>