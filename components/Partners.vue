<script setup lang="ts">
import { Marquee } from "@selemondev/vue3-marquee";
import "@selemondev/vue3-marquee/dist/style.css";

import {
  <PERSON>,
  <PERSON>n,
  Ghost,
  Puzzle,
  Squirrel,
  <PERSON><PERSON>,
  <PERSON>,
} from "lucide-vue-next";
import Icons from "./Icons.vue";

interface PartnersProps {
  icon: string;
  title: string;
  link: string;
}
const partners: PartnersProps[] = [
    {
      title: 'AWS',
      icon: 'aws',
      link: 'https://aws.amazon.com/',
    },
    {
      title: 'Google Cloud',
      icon: 'google',
      link: 'https://cloud.google.com/',
    },
    {
      title: 'Vercel',
      icon: 'vercel',
      link: 'https://vercel.com/',
    },
    {
      title: 'Netlify',
      icon: 'netlify',
      link: 'https://www.netlify.com/',
    },
    {
      title: 'Cloudflare',
      icon: 'cloudflare',
      link: 'https://www.cloudflare.com/',
    },
    {
      title: 'Coolify',
      icon: 'coolify',
      link: 'https://coolify.io/',
    },
    {
      title: 'Thirdweb',
      icon: 'thirdweb',
      link: 'https://thirdweb.com/',
    },
    {
      title: 'Spheron',
      icon: 'spheron',
      link: 'https://spheron.network/',
    },
    {
      title: 'The Graph',
      icon: 'graph',
      link: 'https://thegraph.com/',
    },
    {
      title: 'OpenAI',
      icon: 'openai',
      link: 'https://openai.com/',
    },
  ]

</script>

<template>
  <section
    id="partners"
    class="max-w-[75%] mx-auto py-24 sm:py-32"
  >
    <h2 class="text-lg text-center mb-6">Our Technology Partners</h2>

    <div class="mx-auto">
      <Marquee
        class="gap-[3rem]"
        :pauseOnHover="true"
        :fade="true"
        innerClassName="gap-[3rem]"
      >
        <div
          v-for="{ icon, title, link } in partners"
          :key="title"
        >
              <a target="_blank" :href="link" class="m-auto h-20 w-48 flex items-center rounded-xl border bg-white border-gray-50/[.1] p-3 text-center">
                <div class="m-auto h-10 w-full">
                  <Icons :name="icon" />
                </div>
              </a>
        </div>
      </Marquee>
    </div>
  </section>
</template>
