<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import emailjs from '@emailjs/browser';
import { But<PERSON> } from "./ui/button";
import { <PERSON>, <PERSON>Header, CardContent, CardFooter } from "./ui/card";
import { Label } from "./ui/label";
import { Input } from "./ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { Textarea } from "./ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Building2, Phone, Mail, Clock } from "lucide-vue-next";

const config = useRuntimeConfig();

const ENV_CONSTANTS = {
  EMAILJS_API: config.public.emailjs_api,
  EMAILJS_SERVICE_ID: config.public.emailjs_service_id,
  EMAILJS_TEMPLATE_ID: config.public.emailjs_template_id,
};

interface ContactFormProps {
  firstName: string;
  lastName: string;
  email: string;
  subject: string;
  message: string;
}

const contactForm = reactive<ContactFormProps>({
  firstName: "",
  lastName: "",
  email: "",
  subject: "Web Development",
  message: "",
});

const formValidation = reactive({
  firstName: { status: false, message: '' },
  lastName: { status: false, message: '' },
  email: { status: false, message: '' },
  message: { status: false, message: '' },
});

const isSending = ref(false);
const showAlert = ref(false);
const alertMessage = ref({ message: '', status: 'success' });

// Email validation function
function isEmailFormat(email: string) {
  const atIndex = email.indexOf('@');
  if (atIndex === -1 || atIndex !== email.lastIndexOf('@'))
    return false;
  const [username, domain] = email.split('@');
  if (!username || !domain)
    return false;
  const domainParts = domain.split('.');
  return domainParts.length > 1 && domainParts.every(part => !!part);
}

// Validation functions
function validateFirstName() {
  if (contactForm.firstName.length < 2)
    formValidation.firstName = { status: false, message: 'First name is required' };
  else
    formValidation.firstName = { status: true, message: '' };
}

function validateLastName() {
  if (contactForm.lastName.length < 2)
    formValidation.lastName = { status: false, message: 'Last name is required' };
  else
    formValidation.lastName = { status: true, message: '' };
}

function validateEmail() {
  if (!isEmailFormat(contactForm.email))
    formValidation.email = { status: false, message: 'Please enter a valid email' };
  else
    formValidation.email = { status: true, message: '' };
}

function validateMessage() {
  if (contactForm.message.length < 50)
    formValidation.message = { status: false, message: 'Message should be at least 50 characters' };
  else
    formValidation.message = { status: true, message: '' };
}

const isFormValid = computed(() => {
  return formValidation.firstName.status && 
         formValidation.lastName.status && 
         formValidation.email.status && 
         formValidation.message.status;
});

async function handleSubmit() {
  // Validate all fields
  validateFirstName();
  validateLastName();
  validateEmail();
  validateMessage();

  if (!isFormValid.value) {
    showAlert.value = true;
    alertMessage.value = { message: 'Please check all fields are filled correctly', status: 'error' };
    return;
  }

  isSending.value = true;
  
  // Prepare EmailJS data
  const emailData = {
    name: `${contactForm.firstName} ${contactForm.lastName}`,
    email: contactForm.email,
    subject: contactForm.subject,
    message: contactForm.message,
  };

  try {
    const result = await emailjs.send(
      ENV_CONSTANTS.EMAILJS_SERVICE_ID,
      ENV_CONSTANTS.EMAILJS_TEMPLATE_ID,
      emailData,
      ENV_CONSTANTS.EMAILJS_API
    );

    if (result.status === 200) {
      showAlert.value = true;
      alertMessage.value = { message: 'Message sent successfully!', status: 'success' };
      // Reset form
      contactForm.firstName = "";
      contactForm.lastName = "";
      contactForm.email = "";
      contactForm.subject = "Web Development";
      contactForm.message = "";
    }
  } catch (error) {
    showAlert.value = true;
    alertMessage.value = { message: 'Failed to send message. Please try again.', status: 'error' };
  } finally {
    isSending.value = false;
    setTimeout(() => {
      showAlert.value = false;
    }, 5000);
  }
}
const CONTACT_DETAILS = {
  title: 'Contact Us',
  description: 'At Dappgenie, we are always here to help. Whether you have a question, a project idea, or just want to learn more about what we do, we would love to hear from you.',
  contactInfo: 'You can reach us through the following channels.',
  PHONE: {
    content: '+91 8590233469',
  },
  MAIL: {
    content: '<EMAIL>',
  },
  ADDRESS: {
    title: 'Our office is located at',
    content: 'No 408 Cita Building, 1st Floor 1st Main, Koramangala 7th Block, Bengaluru, 560095',
  },
  HOURS: {
    title: 'Hours of Operation',
    content: 'We are available to take your call from Monday to Friday, 9:00 AM to 5:00 PM IST.',
  },
  info: 'If you would like to leave a message outside of our business hours, you can use the form below to send us an email. We will get back to you as soon as possible.',
}
</script>

<template>
  <section
    id="contact"
    class="container py-24 sm:py-32"
  >
    <section class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div>
        <div class="mb-4">
          <h2 class="text-lg text-primary mb-2 tracking-wider">Contact</h2>

          <h2 class="text-3xl md:text-4xl font-bold">Connect With Us</h2>
        </div>
        <p class="mb-8 text-muted-foreground lg:w-5/6">
          {{ CONTACT_DETAILS.description }}
        </p>

        <div class="flex flex-col gap-4">
          <div>
            <div class="flex gap-2 mb-1">
              <Building2 />
              <div class="font-bold">Find Us</div>
            </div>

            <div>{{ CONTACT_DETAILS.ADDRESS.content }}</div>
          </div>

          <div>
            <div class="flex gap-2 mb-1">
              <Phone />
              <div class="font-bold">Call Us</div>
            </div>

            <div>{{ CONTACT_DETAILS.PHONE.content }}</div>
          </div>

          <div>
            <div class="flex gap-2 mb-1">
              <Mail />
              <div class="font-bold">Mail Us</div>
            </div>

            <div>{{ CONTACT_DETAILS.MAIL.content }}</div>
          </div>

          <div>
            <div class="flex gap-2">
              <Clock />
              <div class="font-bold">Reach Out To Us</div>
            </div>

            <div>
              <div>Monday - Friday</div>
              <div>10AM - 6PM</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form -->
      <Card>
        <CardContent  class="!p-3 sm:!p-6">
          <form @submit.prevent="handleSubmit" class="grid gap-4">
            <div class="flex flex-col md:flex-row gap-8">
              <div class="flex flex-col w-full gap-1.5">
                <Label for="first-name">First Name</Label>
                <Input
                  id="first-name"
                  type="text"
                  placeholder="First Name"
                  v-model="contactForm.firstName"
                  @input="validateFirstName"
                />
                <span v-if="!formValidation.firstName.status" class="text-xs text-destructive">
                  {{ formValidation.firstName.message }}
                </span>
              </div>

              <div class="flex flex-col w-full gap-1.5">
                <Label for="last-name">Last Name</Label>
                <Input
                  id="last-name"
                  type="text"
                  placeholder="Last Name"
                  v-model="contactForm.lastName"
                  @input="validateLastName"
                />
                <span v-if="!formValidation.lastName.status" class="text-xs text-destructive">
                  {{ formValidation.lastName.message }}
                </span>
              </div>
            </div>

            <div class="flex flex-col gap-1.5">
              <Label for="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                v-model="contactForm.email"
                @input="validateEmail"
              />
              <span v-if="!formValidation.email.status" class="text-xs text-destructive">
                {{ formValidation.email.message }}
              </span>
            </div>

            <div class="flex flex-col gap-1.5">
              <Label for="subject">Subject</Label>
              <Select v-model="contactForm.subject">
                <SelectTrigger>
                  <SelectValue placeholder="Select a subject" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="Web Development">Web Development</SelectItem>
                    <SelectItem value="Mobile Development">Mobile Development</SelectItem>
                    <SelectItem value="Blockchain Development">Blockchain Development</SelectItem>
                    <SelectItem value="AI Development">AI Development</SelectItem>
                    <SelectItem value="Smart Contract">Smart Contract</SelectItem>
                    <SelectItem value="DApp Development">DApp Development</SelectItem>
                    <SelectItem value="Blockchain Consulting">Blockchain Consulting</SelectItem>

                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <div class="flex flex-col gap-1.5">
              <Label for="message">Message</Label>
              <Textarea
                id="message"
                placeholder="Your message..."
                rows="5"
                v-model="contactForm.message"
                @input="validateMessage"
              />
              <span v-if="!formValidation.message.status" class="text-xs text-destructive">
                {{ formValidation.message.message }}
              </span>
            </div>

            <Alert
              v-if="showAlert"
              :variant="alertMessage.status === 'success' ? 'default' : 'destructive'"
            >
              <AlertCircle class="w-4 h-4" />
              <AlertTitle>{{ alertMessage.status === 'success' ? 'Success' : 'Error' }}</AlertTitle>
              <AlertDescription>
                {{ alertMessage.message }}
              </AlertDescription>
            </Alert>

            <Button 
              type="submit" 
              class="mt-4 !bg-primary text-primary-foreground hover:!bg-primary/80"
              :disabled="isSending"
            >
              <template v-if="isSending">
                <span class="flex items-center gap-2">
                  <span class="animate-spin">⏳</span>
                  Sending...
                </span>
              </template>
              <template v-else>
                Send message
              </template>
            </Button>
          </form>
        </CardContent>
      </Card>
    </section>
  </section>
</template>