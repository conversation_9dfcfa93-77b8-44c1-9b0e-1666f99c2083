<script setup lang="ts">
import { ChevronRight } from 'lucide-vue-next';
defineProps<{
    href: string;
    class?: string;
}>()
</script>

<template>
    <NuxtLink :href="href" :class="class" class="link-outline">
    <span class="link-content">
      <span class="link-text">
        <slot />
      </span>
      <ChevronRight class="icon" />
    </span>
  </NuxtLink>
</template>

<style scoped>
.link-outline {
    @apply rounded-lg p-[1.5px] mx-auto mb-8 w-fit flex justify-center items-center gap-x-2;
  background-image: linear-gradient(144deg, #1a94bd, #006587 50%, #00ddeb);
  text-decoration: none;
  box-shadow: #00dbeb1d 0 15px 30px -5px;
  transition: all 0.3s;
}

.link-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  @apply bg-background;
  padding: 0.5rem 1rem;
  @apply rounded-md;
  transition: background-color 300ms;
  font-size: 0.875rem;
}

.link-text {
  @apply text-transparent;
  background-image: linear-gradient(144deg, #1a94bd, #ffffff 50%, #00ddeb);
  background-clip: text;
  -webkit-background-clip: text;
}

.icon {
  width: 1rem;
  height: 1rem;
  color: #00ddeb;
}

/* Hover states */
.link-outline:hover .link-content {
    @apply bg-transparent;
}

.link-outline:hover .link-text,
.link-outline:hover .icon {
  @apply text-primary;
  background-image: none;
}

/* Active state */
.link-outline:active {
  transform: scale(0.97);
}
</style>