<script setup lang="ts">
import {
  Accordion,
  Accordion<PERSON><PERSON>nt,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface FAQProps {
  question: string;
  answer: string;
  value: string;
}

const FAQList: FAQProps[] = [
  {
    value: 'item-1',
    question: "What blockchain platforms does <PERSON><PERSON><PERSON><PERSON> specialize in?",
    answer: "We specialize in multiple blockchain platforms including Ethereum, Binance Smart Chain, Polygon, Solana, and other major networks. Our expertise allows us to recommend and implement the best blockchain solution for your specific needs."
  },
  {
    value: 'item-2',
    question: "How long does it typically take to develop a blockchain application?",
    answer: "Development timelines vary based on project complexity, ranging from 4-12 weeks for basic applications to 3-6 months for complex enterprise solutions. We provide detailed timelines during initial consultation based on your specific requirements."
  },
  {
    value: 'item-3',
    question: "What sets Dappgenie apart from other blockchain development companies?",
    answer: "Our combination of extensive blockchain expertise, proven track record, dedicated project management, and post-deployment support sets us apart. We focus on delivering scalable, secure solutions while ensuring transparent communication throughout the development process."
  },
  {
    value: 'item-4',
    question: "Do you provide ongoing support after project completion?",
    answer: "Yes, we offer comprehensive post-deployment support including maintenance, updates, security monitoring, and technical assistance. We also provide training to your team to ensure smooth operation of the implemented solutions."
  },
  {
    value: 'item-5',
    question: "How do you ensure the security of blockchain applications?",
    answer: "We implement multiple security measures including smart contract audits, penetration testing, code reviews, and industry best practices. Our security-first approach includes regular updates and vulnerability assessments to protect your blockchain assets."
  },
  {
    value: 'item-6',
    question: "Can you help migrate existing systems to blockchain?",
    answer: "Yes, we specialize in helping businesses transition from traditional systems to blockchain-based solutions. Our team analyzes your current infrastructure and develops a strategic migration plan that minimizes disruption to your operations."
  },
  {
    value: 'item-7',
    question: "What is your development process like?",
    answer: "Our development process follows an agile methodology with clear milestones and regular client updates. It includes requirements analysis, design, development, testing, deployment, and post-launch support, ensuring quality and transparency at every stage."
  },
  {
    value: 'item-8',
    question: "Do you offer blockchain consulting services?",
    answer: "Yes, we provide comprehensive blockchain consulting services including technology assessment, feasibility studies, strategy development, and implementation roadmaps to help businesses maximize the benefits of blockchain technology."
  },
  {
    value: 'item-9',
    question: "How do you handle project scalability and performance?",
    answer: "We design our solutions with scalability in mind, using advanced architecture patterns, optimal smart contract design, and efficient data structures. Our applications are tested under various load conditions to ensure consistent performance."
  },
  {
    value: 'item-10',
    question: "What industries have you worked with?",
    answer: "We have extensive experience across multiple industries including finance, healthcare, supply chain, real estate, and education. Our diverse portfolio demonstrates our ability to deliver customized blockchain solutions for specific industry needs."
  }
];
</script>

<template>
  <section
    id="faqs"
    class="container md:w-[700px] py-24 sm:py-32"
  >
    <div class="text-center mb-8">
      <h2 class="text-lg text-primary text-center mb-2 tracking-wider">FAQS</h2>

      <h2 class="text-3xl md:text-4xl text-center font-bold">
        Common Questions
      </h2>
    </div>

    <Accordion
      type="single"
      collapsible
      class="AccordionRoot"
    >
      <AccordionItem
        v-for="{ question, answer, value } in FAQList"
        :key="value"
        :value="value"
        class="border-none backdrop-blur-[50px] bg-[rgb(115,115,115)]/[0.07] shadow-[inset_2px_4px_16px_0px_rgba(248,248,248,0.06)]"
      >
        <AccordionTrigger class="text-left"> {{ question }} </AccordionTrigger>

        <AccordionContent>{{ answer }}</AccordionContent>
      </AccordionItem>
    </Accordion>

    <h3 class="font-medium mt-4">
      Still have questions?
        <a
          href="/contact"
          class="underline text-muted-foreground"
          >Contact us</a
        >
    </h3>
  </section>
</template>
