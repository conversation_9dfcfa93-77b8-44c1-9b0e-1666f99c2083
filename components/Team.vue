<script setup lang="ts">
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>oot<PERSON>,
} from "@/components/ui/card";

import LinkedInIcon from "@/icons/LinkedInIcon.vue";
import GithubIcon from "@/icons/GithubIcon.vue";
import XIcon from "@/icons/XIcon.vue";

interface TeamProps {
  imageUrl: string;
  firstName: string;
  lastName: string;
  positions: string[];
  socialNetworks: SocialNetworkProps[];
}

interface SocialNetworkProps {
  name: string;
  url: string;
}

const teamList: TeamProps[] = [
  {
    imageUrl:
      "/images/team/girl_1.jpeg",
    firstName: "Mekha",
    lastName: "Krishnan",
    positions: ["Co-founder", "CEO", "Product Owner", "Blockchain Developer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/mekhakrishnanm/",
      },
      {
        name: "<PERSON>",
        url: "https://x.com/Me<PERSON><PERSON><PERSON>hnanM",
      },
    ],
  },
  {
    
    imageUrl: "/images/team/boy_1.jpeg",
    firstName: "<PERSON>",
    lastName: "Sam",
    positions: ["Co-founder", "<PERSON><PERSON>", "DevOps Engineer", "Blockchain Developer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/denniarems/",
      },
      {
        name: "X",
        url: "https://x.com/denniarems",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/boy_2.jpeg",
    firstName: "Anas",
    lastName: "KVM",
    positions: ["Co-founder", "Blockchain Developer & Architect", "AI Engineer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/leopoldo-miranda/",
      },
      {
        name: "X",
        url: "https://x.com/kvm_anas",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/boy_3.jpeg",
    firstName: "Delbin",
    lastName: "Thomas",
    positions: ["Co-founder", "COO", "Blockchain Enthusiast"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/delbinthomas/",
      },
      {
        name: "X",
        url: "https://x.com/delbint",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/boy_1.jpeg",
    firstName: "Farasul",
    lastName: "Nisan",
    positions: ["Co-founder", "CFO", "Blockchain Enthusiast"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/farasul-nisan-5a439122/",
      },
      {
        name: "X",
        url: "https://x.com/FarasulN",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/girl_2.jpeg",
    firstName: "Carmal",
    lastName: "Maria",
    positions: ["Project Manager", "Scrum Master"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/carmalmariyalinked",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/boy_2.jpeg",
    firstName: "Achu",
    lastName: "Vyas",
    positions: ["Software Full Stack Engineer", "Mobile App Developer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/achu-vyas",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/boy_1.jpeg",
    firstName: "Razal",
    lastName: "MP",
    positions: ["Software Full Stack Engineer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/razalp",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/boy_3.jpeg",
    firstName: "Ashish",
    lastName: "Sharma",
    positions: ["Software Frontend Engineer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/aeshtech"
      },
    ],
  },
  {
    imageUrl:
      "/images/team/boy_1.jpeg",
    firstName: "Favas",
    lastName: "Ali",
    positions: ["Software Frontend Engineer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/favas-ali-k-k",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/boy_2.jpeg",
    firstName: "Harshan",
    lastName: "Mathew",
    positions: ["Devops Engineer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/harshan-mathew",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/boy_1.jpeg",
    firstName: "Abin",
    lastName: "Baby",
    positions: ["UI/UX Designer", "Product Designer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/abin-baby",
      },
    ],
  },
  {
    imageUrl:
      "/images/team/girl_2.jpeg",
    firstName: "Sethu",
    lastName: "Lekshmi",
    positions: ["UI/UX Designer", "Graphic Designer"],
    socialNetworks: [
      {
        name: "LinkedIn",
        url: "https://www.linkedin.com/in/sethu-lekshmi-b6887b22a",
      },
    ],
  },
];

const socialIcon = (socialName: string) => {
  switch (socialName) {
    case "LinkedIn":
      return LinkedInIcon;

    case "Github":
      return GithubIcon;

    case "X":
      return XIcon;
  }
};
</script>

<template>
  <section
    id="team"
    class="container lg:w-[75%] py-24 sm:py-32"
  >
    <div class="text-center mb-8">
      <h2 class="text-lg text-primary text-center mb-2 tracking-wider">Team</h2>

      <h2 class="text-3xl md:text-4xl text-center font-bold">
        The Company Dream Team
      </h2>
    </div>

    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
    >
      <Card
        v-for="{
          imageUrl,
          firstName,
          lastName,
          positions,
          socialNetworks,
        } in teamList"
        :key="imageUrl"
        class="flex flex-col h-full overflow-hidden group/hoverimg p-0"
      >
        <CardHeader class="p-0 gap-0">
          <div class="h-full overflow-hidden">
            <NuxtImg
              :src="imageUrl"
              alt=""
              class="w-full aspect-square object-cover saturate-0 transition-all duration-200 ease-linear size-full group-hover/hoverimg:saturate-100 group-hover/hoverimg:scale-[1.01]"
            />
          </div>
          <CardTitle class="pt-6 pb-2 px-3 text-center sm:text-left text-lg"
            >{{ firstName }}
            <span class="text-primary">{{ lastName }}</span>
          </CardTitle>
        </CardHeader>

        <CardContent
          class="text-muted-foreground pb-4 px-3 text-center sm:text-left text-sm"
        >
        <span
          v-for="(position, index) in positions"
          :key="index">
          {{ position }}<span v-if="index < positions.length - 1">, </span>
        </span>
        </CardContent>

        <CardFooter class="space-x-4 mt-auto px-3 flex justify-center sm:justify-start">
          <a
            v-for="{ name, url } in socialNetworks"
            key="name"
            :href="url"
            target="_blank"
            class="hover:opacity-80 transition-all fill-foreground"
            :aria-label="`Visit our ${name} page`"
          >
            <component :is="socialIcon(name)" />
          </a>
        </CardFooter>
      </Card>
    </div>
  </section>
</template>
