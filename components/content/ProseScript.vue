
<!-- components/content/ProseScript.vue -->
<template>
  <div v-if="isDev" class="my-4 p-4 bg-yellow-500/10 text-yellow-400 rounded-lg">
    <p class="flex items-center gap-2">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
        <path d="M12 9v4"/>
        <path d="M12 17h.01"/>
      </svg>
      Rendering the <code class="px-1.5 py-0.5 rounded bg-yellow-500/20">script</code> element is disabled for security.
    </p>
  </div>
</template>

<script setup lang="ts">
defineProps({
  src: {
    type: String,
    default: ''
  }
})
const isDev = import.meta.dev
</script>
