<!-- components/content/ProsePre.vue -->
<template>
  <pre :class="[
    'my-6 rounded-lg p-4 overflow-x-auto relative bg-muted',
    $props.class
  ]">
    <div class="absolute right-4 top-4 opacity-0 transition-opacity group-hover:opacity-100">
      <button 
        @click="copyCode" 
        class="p-2 rounded-md bg-primary/10 hover:bg-primary/20 transition-colors"
        :title="copied ? 'Copied!' : 'Copy code'"
      >
        <svg
          v-if="!copied"
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="w-4 h-4"
        >
          <rect width="8" height="8" x="8" y="8" rx="2" />
          <path d="M16 8v-2a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h2" />
        </svg>
        <svg
          v-else
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="w-4 h-4"
        >
          <polyline points="20 6 9 17 4 12" />
        </svg>
      </button>
    </div>
    <slot />
  </pre>
</template>

<script setup lang="ts">
defineProps({
  code: {
    type: String,
    default: ''
  },
  language: {
    type: String,
    default: null
  },
  filename: {
    type: String,
    default: null
  },
  highlights: {
    type: Array as () => number[],
    default: () => []
  },
  meta: {
    type: String,
    default: null
  },
  class: {
    type: String,
    default: null
  }
})

const copied = ref(false)

const copyCode = () => {
  const code = (document.querySelector('pre code') as HTMLElement)?.innerText
  if (code) {
    navigator.clipboard.writeText(code)
    copied.value = true
    setTimeout(() => {
      copied.value = false
    }, 2000)
  }
}
</script>

<style>
pre code .line {
  display: block;
}
</style>