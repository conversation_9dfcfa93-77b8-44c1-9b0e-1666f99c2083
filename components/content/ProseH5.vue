<template>
  <h5 
    :id="props.id"
    class="scroll-m-20 text-lg font-semibold tracking-tight text-foreground my-4 relative group"
  >
    <a
      v-if="props.id && generate"
      :href="`#${props.id}`"
      class="relative"
    >
      <slot />
      <span class="absolute -left-4 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
        #
      </span>
    </a>
    <slot v-else />
  </h5>
</template>

<script setup lang="ts">
import { computed, useRuntimeConfig } from '#imports'

const props = defineProps<{ id?: string }>()

const { headings } = useRuntimeConfig().public.mdc
const generate = computed(() => props.id && ((typeof headings?.anchorLinks === 'boolean' && headings?.anchorLinks === true) || (typeof headings?.anchorLinks === 'object' && headings?.anchorLinks?.h5)))
</script>
