<script lang="ts" setup>
import { Book, Computer, DollarSign, Gamepad, Hospital, House, PercentDiamond, PlaneTakeoff, ShoppingCart, Truck, Users } from 'lucide-vue-next';

interface IconsProps {
  name: string
}
defineProps<IconsProps>()

const iconMap: Record<
  string,
  | typeof Hospital
  | typeof Book
  | typeof ShoppingCart
  | typeof PercentDiamond
  | typeof Users
  | typeof DollarSign
  | typeof Gamepad
  | typeof Computer
  | typeof PlaneTakeoff
  | typeof House
  | typeof Truck
> = {
  hospital: Hospital,
  education: Book,
  ecommerce: ShoppingCart,
  retail: PercentDiamond,
  social: Users,
  finance: DollarSign,
  sports: Gamepad,
  it: Computer,
  travel: PlaneTakeoff,
  estate: House,
  logistics: Truck,
};
</script>

<template>

  <svg  v-if="name === 'phone'" class="h-full w-full"xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><path fill="#888888" d="M15 12h2a5 5 0 0 0-5-5v2a3 3 0 0 1 3 3m4 0h2c0-5-4.03-9-9-9v2c3.86 0 7 3.13 7 7m1 3.5c-1.25 0-2.45-.2-3.57-.57c-.35-.11-.74-.03-1.02.25l-2.2 2.2a15.1 15.1 0 0 1-6.59-6.59l2.2-2.2c.28-.28.36-.67.25-1.02A11.4 11.4 0 0 1 8.5 4a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1a17 17 0 0 0 17 17a1 1 0 0 0 1-1v-3.5a1 1 0 0 0-1-1"/></svg>
  <svg  v-if="name === 'mail'" class="h-full w-full"xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><path fill="#888888" d="m20 8l-8 5l-8-5V6l8 5l8-5m0-2H4c-1.11 0-2 .89-2 2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2"/></svg>
  <svg  v-if="name === 'twitter'" class="h-full w-full"xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><path fill="none" stroke="#888888" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="m3 21l7.548-7.548M21 3l-7.548 7.548m0 0L8 3H3l7.548 10.452m2.904-2.904L21 21h-5l-5.452-7.548" color="#888888"/></svg>
  <svg  v-if="name === 'linkedin'" class="h-full w-full"xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><path fill="#888888" d="M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2zm-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93zM6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37z"/></svg>
  <svg  v-if="name === 'discord'" class="h-full w-full"xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><path fill="#888888" d="m22 24l-5.25-5l.63 2H4.5A2.5 2.5 0 0 1 2 18.5v-15A2.5 2.5 0 0 1 4.5 1h15A2.5 2.5 0 0 1 22 3.5V24M12 6.8c-2.68 0-4.56 1.15-4.56 1.15c1.03-.92 2.83-1.45 2.83-1.45l-.17-.17c-1.69.03-3.22 1.2-3.22 1.2c-1.72 3.59-1.61 6.69-1.61 6.69c1.4 1.81 3.48 1.68 3.48 1.68l.71-.9c-1.25-.27-2.04-1.38-2.04-1.38S9.3 14.9 12 14.9s4.58-1.28 4.58-1.28s-.79 1.11-2.04 1.38l.71.9s2.08.13 3.48-1.68c0 0 .11-3.1-1.61-6.69c0 0-1.53-1.17-3.22-1.2l-.17.17s1.8.53 2.83 1.45c0 0-1.88-1.15-4.56-1.15m-2.07 3.79c.65 0 1.18.57 1.17 1.27c0 .69-.52 1.27-1.17 1.27c-.64 0-1.16-.58-1.16-1.27c0-.7.51-1.27 1.16-1.27m4.17 0c.65 0 1.17.57 1.17 1.27c0 .69-.52 1.27-1.17 1.27c-.64 0-1.16-.58-1.16-1.27c0-.7.51-1.27 1.16-1.27Z"/></svg>
  <svg  v-if="name === 'whatsapp'" class="h-full w-full"xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><path fill="#888888" d="M12.04 2c-5.46 0-9.91 4.45-9.91 9.91c0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21c5.46 0 9.91-4.45 9.91-9.91c0-2.65-1.03-5.14-2.9-7.01A9.82 9.82 0 0 0 12.04 2m.01 1.67c2.2 0 4.26.86 5.82 2.42a8.23 8.23 0 0 1 2.41 5.83c0 4.54-3.7 8.23-8.24 8.23c-1.48 0-2.93-.39-4.19-1.15l-.3-.17l-3.12.82l.83-3.04l-.2-.32a8.2 8.2 0 0 1-1.26-4.38c.01-4.54 3.7-8.24 8.25-8.24M8.53 7.33c-.16 0-.43.06-.66.31c-.22.25-.87.86-.87 2.07c0 1.22.89 2.39 1 2.56c.14.17 1.76 2.67 4.25 3.73c.59.27 1.05.42 1.41.53c.59.19 1.13.16 1.56.1c.48-.07 1.46-.6 1.67-1.18s.21-1.07.15-1.18c-.07-.1-.23-.16-.48-.27c-.25-.14-1.47-.74-1.69-.82c-.23-.08-.37-.12-.56.12c-.16.25-.64.81-.78.97c-.15.17-.29.19-.53.07c-.26-.13-1.06-.39-2-1.23c-.74-.66-1.23-1.47-1.38-1.72c-.12-.24-.01-.39.11-.5c.11-.11.27-.29.37-.44c.13-.14.17-.25.25-.41c.08-.17.04-.31-.02-.43c-.06-.11-.56-1.35-.77-1.84c-.2-.48-.4-.42-.56-.43c-.14 0-.3-.01-.47-.01"/></svg>
  <svg  v-if="name === 'skype'" class="h-full w-full"xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24"><path fill="#888888" d="M18 6c2.07 2.04 2.85 4.89 2.36 7.55c.41.72.64 1.56.64 2.45a5 5 0 0 1-5 5c-.89 0-1.73-.23-2.45-.64c-2.66.49-5.51-.29-7.55-2.36c-2.07-2.04-2.85-4.89-2.36-7.55C3.23 9.73 3 8.89 3 8a5 5 0 0 1 5-5c.89 0 1.73.23 2.45.64c2.66-.49 5.51.29 7.55 2.36m-5.96 11.16c2.87 0 4.3-1.38 4.3-3.24c0-1.19-.56-2.46-2.73-2.95l-1.99-.44c-.76-.17-1.62-.4-1.62-1.11c0-.72.6-1.22 1.7-1.22c2.23 0 2.02 1.53 3.13 1.53c.58 0 1.08-.34 1.08-.93c0-1.37-2.19-2.4-4.05-2.4c-2.01 0-4.16.86-4.16 3.14c0 1.1.39 2.27 2.55 2.81l2.69.68c.81.2 1.01.65 1.01 1.07c0 .68-.68 1.35-1.91 1.35c-2.41 0-2.08-1.85-3.37-1.85c-.58 0-1 .4-1 .97c0 1.11 1.33 2.59 4.37 2.59"/></svg>


  <svg v-if="name === 'html'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="0.71em" height="1em"
    viewBox="0 0 256 361">
    <path fill="#E44D26" d="m255.555 70.766l-23.241 260.36l-104.47 28.962l-104.182-28.922L.445 70.766z"></path>
    <path fill="#F16529" d="m128 337.95l84.417-23.403l19.86-222.49H128z"></path>
    <path fill="#EBEBEB"
      d="M82.82 155.932H128v-31.937H47.917l.764 8.568l7.85 88.01H128v-31.937H85.739zm7.198 80.61h-32.06l4.474 50.146l65.421 18.16l.147-.04V271.58l-.14.037l-35.568-9.604z">
    </path>
    <path
      d="M24.18 0h16.23v16.035h14.847V0h16.231v48.558h-16.23v-16.26H40.411v16.26h-16.23V0M92.83 16.103H78.544V0h44.814v16.103h-14.295v32.455h-16.23V16.103zM130.47 0h16.923l10.41 17.062L168.203 0h16.93v48.558h-16.164V24.49l-11.166 17.265h-.28L146.35 24.49v24.068h-15.88zm62.74 0h16.235v32.508h22.824v16.05h-39.06z">
    </path>
    <path fill="#FFF"
      d="M127.89 220.573h39.327l-3.708 41.42l-35.62 9.614v33.226l65.473-18.145l.48-5.396l7.506-84.08l.779-8.576H127.89zm0-64.719v.078h77.143l.64-7.178l1.456-16.191l.763-8.568H127.89z">
    </path>
  </svg>
  <svg v-if="name === 'css'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="22.7" height="32"
    viewBox="0 0 256 361">
    <path fill="#264DE4" d="M127.844 360.088L23.662 331.166L.445 70.766h255.11l-23.241 260.36z" />
    <path fill="#2965F1" d="m212.417 314.547l19.86-222.49H128V337.95z" />
    <path fill="#EBEBEB"
      d="m53.669 188.636l2.862 31.937H128v-31.937zm-5.752-64.641l2.903 31.937H128v-31.937zM128 271.58l-.14.037l-35.568-9.604l-2.274-25.471h-32.06l4.474 50.146l65.421 18.16l.147-.04z" />
    <path
      d="M60.484 0h38.68v16.176H76.66v16.176h22.506v16.175H60.484zm46.417 0h38.681v14.066h-22.505v2.813h22.505v32.352h-38.68V34.46h22.505v-2.813H106.9zm46.418 0H192v14.066h-22.505v2.813H192v32.352h-38.681V34.46h22.505v-2.813H153.32z" />
    <path fill="#FFF"
      d="m202.127 188.636l5.765-64.641H127.89v31.937h45.002l-2.906 32.704H127.89v31.937h39.327l-3.708 41.42l-35.62 9.614v33.226l65.473-18.145l.48-5.396l7.506-84.08z" />
  </svg>
  <svg v-if="name === 'tailwind'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="53.2" height="32"
    viewBox="0 0 256 154">
    <defs>
      <linearGradient id="logosTailwindcssIcon0" x1="-2.778%" x2="100%" y1="32%" y2="67.556%">
        <stop offset="0%" stop-color="#2298BD" />
        <stop offset="100%" stop-color="#0ED7B5" />
      </linearGradient>
    </defs>
    <path fill="url(#logosTailwindcssIcon0)"
      d="M128 0Q76.8 0 64 51.2Q83.2 25.6 108.8 32c9.737 2.434 16.697 9.499 24.401 17.318C145.751 62.057 160.275 76.8 192 76.8q51.2 0 64-51.2q-19.2 25.6-44.8 19.2c-9.737-2.434-16.697-9.499-24.401-17.318C174.249 14.743 159.725 0 128 0M64 76.8q-51.2 0-64 51.2q19.2-25.6 44.8-19.2c9.737 2.434 16.697 9.499 24.401 17.318C81.751 138.857 96.275 153.6 128 153.6q51.2 0 64-51.2q-19.2 25.6-44.8 19.2c-9.737-2.434-16.697-9.499-24.401-17.318C110.249 91.543 95.725 76.8 64 76.8" />
  </svg>
  <svg v-if="name === 'unocss'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="32" height="32"
    viewBox="0 0 256 256">
    <path fill="#858585"
      d="M137.176 195.927c0-32.813 26.6-59.412 59.412-59.412S256 163.114 256 195.927c0 32.812-26.6 59.412-59.412 59.412s-59.412-26.6-59.412-59.412" />
    <path fill="#CCC"
      d="M137.176 59.412C137.176 26.6 163.776 0 196.588 0S256 26.6 256 59.412v53.471c0 3.28-2.66 5.941-5.941 5.941H143.117a5.94 5.94 0 0 1-5.941-5.94z" />
    <path fill="#4D4D4D"
      d="M118.824 195.927c0 32.812-26.6 59.412-59.412 59.412S0 228.74 0 195.927v-53.47a5.94 5.94 0 0 1 5.941-5.942h106.942a5.94 5.94 0 0 1 5.941 5.941z" />
  </svg>
  <svg v-if="name === 'javascript'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="32" height="32"
    viewBox="0 0 256 256">
    <path fill="#F7DF1E" d="M0 0h256v256H0z" />
    <path
      d="m67.312 213.932l19.59-11.856c3.78 6.701 7.218 12.371 15.465 12.371c7.905 0 12.89-3.092 12.89-15.12v-81.798h24.057v82.138c0 24.917-14.606 36.259-35.916 36.259c-19.245 0-30.416-9.967-36.087-21.996m85.07-2.576l19.588-11.341c5.157 8.421 11.859 14.607 23.715 14.607c9.969 0 16.325-4.984 16.325-11.858c0-8.248-6.53-11.17-17.528-15.98l-6.013-2.58c-17.357-7.387-28.87-16.667-28.87-36.257c0-18.044 13.747-31.792 35.228-31.792c15.294 0 26.292 5.328 34.196 19.247l-18.732 12.03c-4.125-7.389-8.591-10.31-15.465-10.31c-7.046 0-11.514 4.468-11.514 10.31c0 7.217 4.468 10.14 14.778 14.608l6.014 2.577c20.45 8.765 31.963 17.7 31.963 37.804c0 21.654-17.012 33.51-39.867 33.51c-22.339 0-36.774-10.654-43.819-24.574" />
  </svg>
  <svg v-if="name === 'typescript'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="32" height="32"
    viewBox="0 0 256 256">
    <path fill="#3178C6"
      d="M20 0h216c11.046 0 20 8.954 20 20v216c0 11.046-8.954 20-20 20H20c-11.046 0-20-8.954-20-20V20C0 8.954 8.954 0 20 0" />
    <path fill="#FFF"
      d="M150.518 200.475v27.62q6.738 3.453 15.938 5.179T185.849 235q9.934 0 18.874-1.899t15.678-6.257q6.738-4.359 10.669-11.394q3.93-7.033 3.93-17.391q0-7.51-2.246-13.163a30.8 30.8 0 0 0-6.479-10.055q-4.232-4.402-10.149-7.898t-13.347-6.602q-5.442-2.245-9.761-4.359t-7.342-4.316q-3.024-2.2-4.665-4.661t-1.641-5.567q0-2.848 1.468-5.135q1.469-2.288 4.147-3.927t6.565-2.547q3.887-.906 8.638-.906q3.456 0 7.299.518q3.844.517 7.732 1.597a54 54 0 0 1 7.558 2.719a41.7 41.7 0 0 1 6.781 3.797v-25.807q-6.306-2.417-13.778-3.582T198.633 107q-9.847 0-18.658 2.115q-8.811 2.114-15.506 6.602q-6.694 4.49-10.582 11.437Q150 134.102 150 143.769q0 12.342 7.127 21.06t21.638 14.759a292 292 0 0 1 10.625 4.575q4.924 2.244 8.509 4.66t5.658 5.265t2.073 6.474a9.9 9.9 0 0 1-1.296 4.963q-1.295 2.287-3.93 3.97t-6.565 2.632t-9.2.95q-8.983 0-17.794-3.151t-16.327-9.451m-46.036-68.733H140V109H41v22.742h35.345V233h28.137z" />
  </svg>
  <svg v-if="name === 'solidity'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="20.59" height="32"
    viewBox="0 0 256 398">
    <path d="m191.513 0l-63.867 113.512H0L63.823 0z" opacity=".45" />
    <path d="M127.646 113.512h127.691L191.513 0H63.823z" opacity=".6" />
    <path d="m63.823 226.981l63.823-113.469L63.823 0L0 113.512z" opacity=".8" />
    <path d="m64.442 397.25l63.867-113.513H256L192.132 397.25z" opacity=".45" />
    <path d="M128.309 283.737H.618L64.441 397.25h127.691z" opacity=".6" />
    <path d="m192.132 170.269l-63.823 113.468l63.823 113.513L256 283.737z" opacity=".8" />
  </svg>
  <svg v-if="name === 'ethers'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="50.57" height="32"
    viewBox="0 0 256 162">
    <path fill="#24339B"
      d="M256 138.739c-133.839 17.491-229.367 38.5-230.78-47.733c0 0 2.92-33.412 43.902-35.516c0 0 1.381-29.676 32.69-33.036c16.832-1.822 35.956 15.513 38.029 33.758c0 0 41.42-7.662 43.21 32.722c.628 14.068-2.544 37.997-42.488 36.96c0 0-23.112-3.14-26.253-38.876c-6.5 69.18 93.518 65.161 94.963 2.324c.628-27.164-16.77-55.112-55.96-49.868c-21.48-54.044-78.665-50.935-99.956-.816c-30.43 0-53.699 23.426-53.353 54.013C1.166 191.119 138.176 161.443 256 138.739" />
  </svg>
  <svg v-if="name === 'hardhat'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="46.29" height="32"
    viewBox="0 0 256 177">
    <path fill="#F0D50C"
      d="M256 148.407s-38.993-5.777-59.597-7.434c-19.788-1.59-43.236-2.508-68.392-2.508s-48.603.918-68.385 2.508c-20.61 1.68-59.598 9.695-59.598 9.695v10.076c0 8.956 57.303 16.233 127.983 16.233s127.983-7.277 127.983-16.233zM88.66 14.764c-46.734 16.652-77.943 60.9-77.946 110.513v16.614a284 284 0 0 1 50.804-7.366a274 274 0 0 1-.18-9.634c.017-43.594 10.54-82.822 27.322-110.127" />
    <path fill="#FFF04D"
      d="M245.303 125.277A116.8 116.8 0 0 0 218.53 50.69a243.2 243.2 0 0 1 11.105 74.212q0 6.87-.352 13.58a144 144 0 0 1 15.835 3.359z" />
    <path fill="#FFEA00"
      d="M245.303 125.277A116.8 116.8 0 0 0 218.53 50.69a243.2 243.2 0 0 1 11.105 74.212q0 6.87-.352 13.58a144 144 0 0 1 15.835 3.359z" />
    <path fill="#FFF04D"
      d="M218.53 50.684C187.104 12.59 135.2-1.766 88.666 14.764c-16.793 27.305-27.31 66.533-27.31 110.133q0 4.853.178 9.633c17.465-1.349 37.683-2.183 59.335-2.334h7.154a684 684 0 0 1 101.27 6.308q.343-6.716.354-13.58a243.2 243.2 0 0 0-11.117-74.24" />
    <path fill="#FFF04D"
      d="m175.194 58.442l-7.434-46.46a8.96 8.96 0 0 0-6.292-7.215a117.8 117.8 0 0 0-66.348 0a8.96 8.96 0 0 0-6.286 7.215l-7.439 46.46m46.628 72.768h-7.16c-67.355.471-120.83 7.546-120.83 16.206v12.55a3.13 3.13 0 0 0 .443 2.115a29.9 29.9 0 0 1 11.531-5.295a225.6 225.6 0 0 1 32.36-5.418a21.67 21.67 0 0 1 16.977 5.541a45.67 45.67 0 0 0 30.843 11.985h71.649a45.68 45.68 0 0 0 30.843-11.985a21.66 21.66 0 0 1 16.983-5.547a225.5 225.5 0 0 1 32.354 5.419a27.05 27.05 0 0 1 10.937 4.786c.185.184.398.347.56.514a3.15 3.15 0 0 0 .448-2.127v-12.55c.039-8.928-57.263-16.193-127.938-16.193" />
    <path fill="#6E6F70" d="m154.98 93.942l-26.991 16.614v21.578z" />
    <path fill="#0A0A0A" d="M128.017 132.134v-21.578l-26.992-16.614zm-26.992-48.049l26.992 15.606V40.429z" />
    <path fill="#6E6F70" d="m154.98 84.085l-26.991-43.661v59.261l26.991-15.606z" />
  </svg>


  <NuxtImg v-if="name === 'viem'" src="/images/icons/viem.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <NuxtImg v-if="name === 'wagmi'" src="/images/icons/wagmi.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <NuxtImg v-if="name === 'foundry'" src="/images/icons/foundry.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <svg v-if="name === 'vue'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="37.07" height="32"
    viewBox="0 0 256 221">
    <path fill="#41B883" d="M204.8 0H256L128 220.8L0 0h97.92L128 51.2L157.44 0z" />
    <path fill="#41B883" d="m0 0l128 220.8L256 0h-51.2L128 132.48L50.56 0z" />
    <path fill="#35495E" d="M50.56 0L128 133.12L204.8 0h-47.36L128 51.2L97.92 0z" />
  </svg>
  <svg v-if="name === 'nuxt'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="48.77" height="32"
    viewBox="0 0 256 168">
    <path fill="#00DC82"
      d="M143.618 167.029h95.166c3.023 0 5.992-.771 8.61-2.237a16.96 16.96 0 0 0 6.302-6.115a16.3 16.3 0 0 0 2.304-8.352c0-2.932-.799-5.811-2.312-8.35L189.778 34.6a16.97 16.97 0 0 0-6.301-6.113a17.6 17.6 0 0 0-8.608-2.238c-3.023 0-5.991.772-8.609 2.238a16.96 16.96 0 0 0-6.3 6.113l-16.342 27.473l-31.95-53.724a17 17 0 0 0-6.304-6.112A17.64 17.64 0 0 0 96.754 0c-3.022 0-5.992.772-8.61 2.237a17 17 0 0 0-6.303 6.112L2.31 141.975a16.3 16.3 0 0 0-2.31 8.35c0 2.932.793 5.813 2.304 8.352a16.96 16.96 0 0 0 6.302 6.115a17.6 17.6 0 0 0 8.61 2.237h59.737c23.669 0 41.123-10.084 53.134-29.758l29.159-48.983l15.618-26.215l46.874 78.742h-62.492zm-67.64-26.24l-41.688-.01L96.782 35.796l31.181 52.492l-20.877 35.084c-7.976 12.765-17.037 17.416-31.107 17.416" />
  </svg>
  <svg v-if="name === 'react'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="35.93" height="32"
    viewBox="0 0 256 228">
    <path fill="#00D8FF"
      d="M210.483 73.824a172 172 0 0 0-8.24-2.597c.465-1.9.893-3.777 1.273-5.621c6.238-30.281 2.16-54.676-11.769-62.708c-13.355-7.7-35.196.329-57.254 19.526a171 171 0 0 0-6.375 5.848a156 156 0 0 0-4.241-3.917C100.759 3.829 77.587-4.822 63.673 3.233C50.33 10.957 46.379 33.89 51.995 62.588a171 171 0 0 0 1.892 8.48c-3.28.932-6.445 1.924-9.474 2.98C17.309 83.498 0 98.307 0 113.668c0 15.865 18.582 31.778 46.812 41.427a146 146 0 0 0 6.921 2.165a168 168 0 0 0-2.01 9.138c-5.354 28.2-1.173 50.591 12.134 58.266c13.744 7.926 36.812-.22 59.273-19.855a146 146 0 0 0 5.342-4.923a168 168 0 0 0 6.92 6.314c21.758 18.722 43.246 26.282 56.54 18.586c13.731-7.949 18.194-32.003 12.4-61.268a145 145 0 0 0-1.535-6.842c1.62-.48 3.21-.974 4.76-1.488c29.348-9.723 48.443-25.443 48.443-41.52c0-15.417-17.868-30.326-45.517-39.844m-6.365 70.984q-2.102.694-4.3 1.345c-3.24-10.257-7.612-21.163-12.963-32.432c5.106-11 9.31-21.767 12.459-31.957c2.619.758 5.16 1.557 7.61 2.4c23.69 8.156 38.14 20.213 38.14 29.504c0 9.896-15.606 22.743-40.946 31.14m-10.514 20.834c2.562 12.94 2.927 24.64 1.23 33.787c-1.524 8.219-4.59 13.698-8.382 15.893c-8.067 4.67-25.32-1.4-43.927-17.412a157 157 0 0 1-6.437-5.87c7.214-7.889 14.423-17.06 21.459-27.246c12.376-1.098 24.068-2.894 34.671-5.345q.785 3.162 1.386 6.193M87.276 214.515c-7.882 2.783-14.16 2.863-17.955.675c-8.075-4.657-11.432-22.636-6.853-46.752a157 157 0 0 1 1.869-8.499c10.486 2.32 22.093 3.988 34.498 4.994c7.084 9.967 14.501 19.128 21.976 27.15a135 135 0 0 1-4.877 4.492c-9.933 8.682-19.886 14.842-28.658 17.94M50.35 144.747c-12.483-4.267-22.792-9.812-29.858-15.863c-6.35-5.437-9.555-10.836-9.555-15.216c0-9.322 13.897-21.212 37.076-29.293c2.813-.98 5.757-1.905 8.812-2.773c3.204 10.42 7.406 21.315 12.477 32.332c-5.137 11.18-9.399 22.249-12.634 32.792a135 135 0 0 1-6.318-1.979m12.378-84.26c-4.811-24.587-1.616-43.134 6.425-47.789c8.564-4.958 27.502 2.111 47.463 19.835a144 144 0 0 1 3.841 3.545c-7.438 7.987-14.787 17.08-21.808 26.988c-12.04 1.116-23.565 2.908-34.161 5.309a160 160 0 0 1-1.76-7.887m110.427 27.268a348 348 0 0 0-7.785-12.803c8.168 1.033 15.994 2.404 23.343 4.08c-2.206 7.072-4.956 14.465-8.193 22.045a381 381 0 0 0-7.365-13.322m-45.032-43.861c5.044 5.465 10.096 11.566 15.065 18.186a322 322 0 0 0-30.257-.006c4.974-6.559 10.069-12.652 15.192-18.18M82.802 87.83a323 323 0 0 0-7.227 13.238c-3.184-7.553-5.909-14.98-8.134-22.152c7.304-1.634 15.093-2.97 23.209-3.984a322 322 0 0 0-7.848 12.897m8.081 65.352c-8.385-.936-16.291-2.203-23.593-3.793c2.26-7.3 5.045-14.885 8.298-22.6a321 321 0 0 0 7.257 13.246c2.594 4.48 5.28 8.868 8.038 13.147m37.542 31.03c-5.184-5.592-10.354-11.779-15.403-18.433c4.902.192 9.899.29 14.978.29c5.218 0 10.376-.117 15.453-.343c-4.985 6.774-10.018 12.97-15.028 18.486m52.198-57.817c3.422 7.8 6.306 15.345 8.596 22.52c-7.422 1.694-15.436 3.058-23.88 4.071a382 382 0 0 0 7.859-13.026a347 347 0 0 0 7.425-13.565m-16.898 8.101a359 359 0 0 1-12.281 19.815a329 329 0 0 1-23.444.823c-7.967 0-15.716-.248-23.178-.732a310 310 0 0 1-12.513-19.846h.001a307 307 0 0 1-10.923-20.627a310 310 0 0 1 10.89-20.637l-.001.001a307 307 0 0 1 12.413-19.761c7.613-.576 15.42-.876 23.31-.876H128c7.926 0 15.743.303 23.354.883a329 329 0 0 1 12.335 19.695a359 359 0 0 1 11.036 20.54a330 330 0 0 1-11 20.722m22.56-122.124c8.572 4.944 11.906 24.881 6.52 51.026q-.518 2.504-1.15 5.09c-10.622-2.452-22.155-4.275-34.23-5.408c-7.034-10.017-14.323-19.124-21.64-27.008a161 161 0 0 1 5.888-5.4c18.9-16.447 36.564-22.941 44.612-18.3M128 90.808c12.625 0 22.86 10.235 22.86 22.86s-10.235 22.86-22.86 22.86s-22.86-10.235-22.86-22.86s10.235-22.86 22.86-22.86" />
  </svg>
  <svg v-if="name === 'next'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="32" height="32"
    viewBox="0 0 256 256">
    <defs>
      <linearGradient id="logosNextjsIcon0" x1="55.633%" x2="83.228%" y1="56.385%" y2="96.08%">
        <stop offset="0%" stop-color="#FFF" />
        <stop offset="100%" stop-color="#FFF" stop-opacity="0" />
      </linearGradient>
      <linearGradient id="logosNextjsIcon1" x1="50%" x2="49.953%" y1="0%" y2="73.438%">
        <stop offset="0%" stop-color="#FFF" />
        <stop offset="100%" stop-color="#FFF" stop-opacity="0" />
      </linearGradient>
      <circle id="logosNextjsIcon2" cx="128" cy="128" r="128" />
    </defs>
    <mask id="logosNextjsIcon3" fill="#fff">
      <use href="#logosNextjsIcon2" />
    </mask>
    <g mask="url(#logosNextjsIcon3)">
      <circle cx="128" cy="128" r="128" />
      <path fill="url(#logosNextjsIcon0)"
        d="M212.634 224.028L98.335 76.8H76.8v102.357h17.228V98.68L199.11 234.446a128 128 0 0 0 13.524-10.418" />
      <path fill="url(#logosNextjsIcon1)" d="M163.556 76.8h17.067v102.4h-17.067z" />
    </g>
  </svg>
  <svg v-if="name === 'angular'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="30.23" height="32"
    viewBox="0 0 256 271">
    <defs>
      <linearGradient id="logosAngularIcon0" x1="25.071%" x2="96.132%" y1="90.929%" y2="55.184%">
        <stop offset="0%" stop-color="#E40035" />
        <stop offset="24%" stop-color="#F60A48" />
        <stop offset="35.2%" stop-color="#F20755" />
        <stop offset="49.4%" stop-color="#DC087D" />
        <stop offset="74.5%" stop-color="#9717E7" />
        <stop offset="100%" stop-color="#6C00F5" />
      </linearGradient>
      <linearGradient id="logosAngularIcon1" x1="21.863%" x2="68.367%" y1="12.058%" y2="68.21%">
        <stop offset="0%" stop-color="#FF31D9" />
        <stop offset="100%" stop-color="#FF5BE1" stop-opacity="0" />
      </linearGradient>
    </defs>
    <path fill="url(#logosAngularIcon0)"
      d="m256 45.179l-9.244 145.158L158.373 0zm-61.217 187.697l-66.782 38.105l-66.784-38.105L74.8 199.958h106.4zM128.001 72.249l34.994 85.076h-69.99zM9.149 190.337L0 45.179L97.627 0z" />
    <path fill="url(#logosAngularIcon1)"
      d="m256 45.179l-9.244 145.158L158.373 0zm-61.217 187.697l-66.782 38.105l-66.784-38.105L74.8 199.958h106.4zM128.001 72.249l34.994 85.076h-69.99zM9.149 190.337L0 45.179L97.627 0z" />
  </svg>
  <svg v-if="name === 'qwik'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="30.12" height="32"
    viewBox="0 0 256 272">
    <path fill="#18B6F6"
      d="m224.803 271.548l-48.76-48.483l-.744.107v-.532L71.606 120.252l25.55-24.667l-15.01-86.12l-71.222 88.247c-12.136 12.226-14.372 32.109-5.642 46.781l44.5 73.788c6.813 11.376 19.163 18.18 32.47 18.074l22.038-.213z" />
    <path fill="#AC7EF4"
      d="m251.414 96.01l-9.795-18.075l-5.11-9.25l-2.023-3.615l-.212.213l-26.829-46.463C200.738 7.125 188.176-.105 174.55 0l-23.527.639l-70.158.213c-13.307.106-25.444 7.123-32.151 18.5l-42.69 84.632L82.353 9.25l100.073 109.937l-17.779 17.968l10.646 86.015l.107-.213v.213h-.213l.213.212l8.304 8.081l40.348 39.445c1.704 1.595 4.472-.318 3.3-2.339l-24.911-49.014l43.436-80.273l1.383-1.595c.533-.638 1.065-1.276 1.491-1.914c8.517-11.589 9.688-27.112 2.662-39.764" />
    <path fill="#FFF" d="M182.746 118.763L82.353 9.358l14.266 85.695l-25.55 24.773L175.08 223.065l-9.368-85.696z" />
  </svg>
  <svg v-if="name === 'solid'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="34.28" height="32"
    viewBox="256 239 256 239">
    <defs>
      <linearGradient id="logosSolidjsIcon0" x1="27.5" x2="152" y1="3" y2="63.5"
        gradientTransform="translate(249.56 233.12)scale(1.61006)" gradientUnits="userSpaceOnUse">
        <stop offset=".1" stop-color="#76b3e1" />
        <stop offset=".3" stop-color="#dcf2fd" />
        <stop offset="1" stop-color="#76b3e1" />
      </linearGradient>
      <linearGradient id="logosSolidjsIcon1" x1="95.8" x2="74" y1="32.6" y2="105.2"
        gradientTransform="translate(249.56 233.12)scale(1.61006)" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#76b3e1" />
        <stop offset=".5" stop-color="#4377bb" />
        <stop offset="1" stop-color="#1f3b77" />
      </linearGradient>
      <linearGradient id="logosSolidjsIcon2" x1="18.4" x2="144.3" y1="64.2" y2="149.8"
        gradientTransform="translate(249.56 233.12)scale(1.61006)" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#315aa9" />
        <stop offset=".5" stop-color="#518ac8" />
        <stop offset="1" stop-color="#315aa9" />
      </linearGradient>
      <linearGradient id="logosSolidjsIcon3" x1="75.2" x2="24.4" y1="74.5" y2="260.8"
        gradientTransform="translate(249.56 233.12)scale(1.61006)" gradientUnits="userSpaceOnUse">
        <stop offset="0" stop-color="#4377bb" />
        <stop offset=".5" stop-color="#1a336b" />
        <stop offset="1" stop-color="#1a336b" />
      </linearGradient>
    </defs>
    <path fill="#76b3e1"
      d="M512 289.472s-85.333-62.791-151.347-48.301l-4.829 1.61c-9.66 3.221-17.711 8.05-22.542 14.491l-3.219 4.829l-24.152 41.862l41.863 8.051c17.71 11.27 40.251 16.101 61.182 11.27l74.063 14.491z" />
    <path fill="url(#logosSolidjsIcon0)"
      d="M512 289.472s-85.333-62.791-151.347-48.301l-4.829 1.61c-9.66 3.221-17.711 8.05-22.542 14.491l-3.219 4.829l-24.152 41.862l41.863 8.051c17.71 11.27 40.251 16.101 61.182 11.27l74.063 14.491z"
      opacity=".3" />
    <path fill="#518ac8"
      d="m333.282 289.472l-6.439 1.611c-27.371 8.05-35.421 33.811-20.932 56.352c16.101 20.931 49.913 32.201 77.284 24.151l99.824-33.811s-85.334-62.792-149.737-48.303" />
    <path fill="url(#logosSolidjsIcon1)"
      d="m333.282 289.472l-6.439 1.611c-27.371 8.05-35.421 33.811-20.932 56.352c16.101 20.931 49.913 32.201 77.284 24.151l99.824-33.811s-85.334-62.792-149.737-48.303"
      opacity=".3" />
    <path fill="url(#logosSolidjsIcon2)"
      d="M465.308 361.925c-18.439-23.036-49.008-32.588-77.283-24.15l-99.823 32.201L256 426.328l180.327 30.592l32.201-57.963c6.441-11.271 4.831-24.15-3.22-37.032" />
    <path fill="url(#logosSolidjsIcon3)"
      d="M433.106 418.277c-18.439-23.036-49.006-32.588-77.282-24.15L256 426.328s85.333 64.402 151.346 48.303l4.83-1.612c27.371-8.049 37.031-33.81 20.93-54.742" />
  </svg>
  <svg v-if="name === 'preact'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="27.68" height="32"
    viewBox="0 0 256 296">
    <path fill="#673AB8" d="m128 0l128 73.9v147.8l-128 73.9L0 221.7V73.9z" />
    <path fill="#FFF"
      d="M34.865 220.478c17.016 21.78 71.095 5.185 122.15-34.704c51.055-39.888 80.24-88.345 63.224-110.126c-17.017-21.78-71.095-5.184-122.15 34.704c-51.055 39.89-80.24 88.346-63.224 110.126m7.27-5.68c-5.644-7.222-3.178-21.402 7.573-39.253c11.322-18.797 30.541-39.548 54.06-57.923s48.303-32.004 69.281-38.442c19.922-6.113 34.277-5.075 39.92 2.148s3.178 21.403-7.573 39.254c-11.322 18.797-30.541 39.547-54.06 57.923c-23.52 18.375-48.304 32.004-69.281 38.441c-19.922 6.114-34.277 5.076-39.92-2.147" />
    <path fill="#FFF"
      d="M220.239 220.478c17.017-21.78-12.169-70.237-63.224-110.126C105.96 70.464 51.88 53.868 34.865 75.648s12.169 70.238 63.224 110.126s105.133 56.485 122.15 34.704m-7.27-5.68c-5.643 7.224-19.998 8.262-39.92 2.148c-20.978-6.437-45.761-20.066-69.28-38.441c-23.52-18.376-42.74-39.126-54.06-57.923c-10.752-17.851-13.218-32.03-7.575-39.254c5.644-7.223 19.999-8.261 39.92-2.148c20.978 6.438 45.762 20.067 69.281 38.442s42.739 39.126 54.06 57.923c10.752 17.85 13.218 32.03 7.574 39.254" />
    <path fill="#FFF"
      d="M127.552 167.667c10.827 0 19.603-8.777 19.603-19.604s-8.776-19.603-19.603-19.603s-19.604 8.777-19.604 19.603s8.777 19.604 19.604 19.604" />
  </svg>
  <svg v-if="name === 'native'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="34.86" height="32"
    viewBox="0 0 256 235">
    <path fill="#09D3AC"
      d="M233.805 104.896c0-13.048-15.358-24.619-38.974-31.683c5.682-23.994 3.2-43.083-8.105-49.54a18.1 18.1 0 0 0-9.071-2.273c-10.549 0-23.88 7.366-37.345 20.111c-13.465-12.65-26.76-19.979-37.289-19.979a17.9 17.9 0 0 0-9.204 2.31c-11.249 6.477-13.578 25.453-7.972 49.333c-23.502 7.026-38.785 18.502-38.823 31.494c-.038 12.991 15.378 24.619 38.993 31.664c-5.681 24.013-3.22 43.083 8.105 49.54a17.8 17.8 0 0 0 9.053 2.273c10.567 0 23.9-7.367 37.364-20.112c13.446 12.65 26.74 19.98 37.288 19.98a18.2 18.2 0 0 0 9.204-2.31c11.249-6.477 13.56-25.453 7.973-49.239c23.501-7.12 38.784-18.597 38.803-31.569m-84.822-59.105c15.51-13.881 25.206-15.377 28.653-15.377a8.9 8.9 0 0 1 4.583 1.098c5.416 3.087 7.575 13.976 5.851 28.407a107 107 0 0 1-1.893 10.965a185 185 0 0 0-23.824-3.92a186.5 186.5 0 0 0-15.605-19.09a48 48 0 0 1 2.235-2.083m-46.947 69.843a255 255 0 0 0 4.678 8.54c1.629 2.86 3.333 5.758 5.227 8.617a178 178 0 0 1-14.847-2.765c1.325-4.81 3.01-9.544 4.942-14.392m-5.245-36.058a167 167 0 0 1 15.017-2.765c-1.894 2.803-3.522 5.682-5.227 8.693c-1.704 3.01-3.2 5.681-4.658 8.56a223 223 0 0 1-5.132-14.488m9.866 25.263a227 227 0 0 1 7.746-14.885a220 220 0 0 1 9.033-14.411c5.51-.417 11.192-.644 17.044-.625s11.363.227 16.836.662c3.143 4.583 6.154 9.47 8.995 14.298c2.84 4.83 5.454 9.886 7.802 14.829a214 214 0 0 1-7.726 14.866c-2.803 4.924-5.89 9.81-9.034 14.43c-5.51.436-11.192.644-17.043.644a210 210 0 0 1-16.836-.682a213 213 0 0 1-9.014-14.298c-2.841-4.942-5.36-9.885-7.727-14.828zm62.23-27.97c5.15.719 10.131 1.647 14.847 2.745a170 170 0 0 1-5 14.298a256 256 0 0 0-4.696-8.54c-1.648-2.86-3.39-5.739-5.076-8.504zm5.226 47.192a250 250 0 0 0 4.659-8.579c1.894 4.924 3.788 9.772 5.113 14.506c-4.772 1.118-9.79 2.027-14.998 2.746a172 172 0 0 0 5.302-8.673zm-33.766-69.748a166 166 0 0 1 10.037 11.799a242 242 0 0 0-9.904-.228c-3.352 0-6.78 0-10.094.228c3.239-4.205 6.553-8.163 9.961-11.799M98.344 31.588a9.13 9.13 0 0 1 4.677-1.06c3.412.142 6.758.986 9.829 2.48a74 74 0 0 1 18.938 12.84l2.215 2.027a186.5 186.5 0 0 0-15.453 18.937a187 187 0 0 0-23.956 3.901c-.833-3.673-1.515-7.272-1.894-10.737c-1.97-14.298.303-25.206 5.625-28.312zm-10.019 96.014a111 111 0 0 1-10.491-3.787c-13.446-5.682-21.778-12.973-21.778-19.203s8.37-13.484 21.797-19.07a109 109 0 0 1 10.302-3.674a185.5 185.5 0 0 0 8.636 22.99a187 187 0 0 0-8.466 22.744m43.557 36.171c-15.51 13.882-25.206 15.359-28.671 15.359a8.9 8.9 0 0 1-4.583-1.099c-5.417-3.086-7.575-13.976-5.852-28.406c.44-3.68 1.072-7.333 1.894-10.946a188.5 188.5 0 0 0 23.823 3.901a184 184 0 0 0 15.605 19.09zm8.655-8.54c-3.409-3.637-6.761-7.576-10.056-11.799c3.257.152 6.571.227 9.923.227s6.78 0 10.094-.208c-3.276 4.204-6.61 8.143-9.98 11.78zm42.004 22.725a9.13 9.13 0 0 1-4.697 1.117c-3.447 0-13.256-1.515-28.71-15.34l-2.215-2.026a186 186 0 0 0 15.434-18.938a184 184 0 0 0 23.975-3.9c.833 3.673 1.496 7.271 1.894 10.737c1.875 14.279-.303 25.187-5.7 28.274zm20.452-53.86a115 115 0 0 1-10.302 3.656a187 187 0 0 0-8.654-22.99a186 186 0 0 0 8.503-22.726a111 111 0 0 1 10.491 3.788c13.427 5.681 21.779 12.972 21.76 19.203c-.02 6.23-8.37 13.407-21.817 18.994zm-62.646-1.93c9.602.01 17.394-7.765 17.404-17.367s-7.764-17.393-17.366-17.403s-17.393 7.764-17.404 17.365a17.366 17.366 0 0 0 17.366 17.404M24.827 0v209.546H256V0zm222.14 200.531H33.88V9.033h213.087zm-160.97-64.198c-5.682 24.013-3.22 43.083 8.104 49.54a17.8 17.8 0 0 0 9.053 2.273c10.567 0 23.9-7.367 37.364-20.112c13.446 12.65 26.74 19.98 37.288 19.98a18.2 18.2 0 0 0 9.204-2.31c11.249-6.477 13.56-25.453 7.973-49.239c23.501-7.045 38.784-18.52 38.803-31.493s-15.358-24.62-38.974-31.683c5.682-23.994 3.2-43.083-8.105-49.541a18.1 18.1 0 0 0-9.071-2.273c-10.549 0-23.88 7.367-37.345 20.112c-13.465-12.65-26.76-19.98-37.289-19.98c-3.22-.07-6.399.728-9.204 2.311c-11.249 6.477-13.578 25.453-7.972 49.333c-23.502 7.026-38.785 18.502-38.823 31.493c-.037 12.992 15.397 24.506 39.012 31.589zm45.847 27.44c-15.51 13.882-25.206 15.359-28.671 15.359a8.9 8.9 0 0 1-4.583-1.099c-5.416-3.086-7.575-13.976-5.852-28.406c.44-3.68 1.072-7.333 1.894-10.946a188.5 188.5 0 0 0 23.823 3.901a184 184 0 0 0 15.605 19.09zm46.947-69.86a256 256 0 0 0-4.697-8.542c-1.647-2.86-3.408-5.681-5.15-8.503c5.15.72 10.131 1.648 14.847 2.746a163 163 0 0 1-4.981 14.298zm5.151 35.98c-4.772 1.118-9.79 2.027-14.999 2.747a219 219 0 0 0 5.227-8.674q2.557-4.47 4.659-8.579c1.988 5.019 3.712 9.83 5.075 14.601zm-9.867-25.281a214 214 0 0 1-7.726 14.866c-2.803 4.924-5.89 9.81-9.033 14.43c-5.511.436-11.193.644-17.044.644a210 210 0 0 1-16.836-.681a213 213 0 0 1-9.014-14.298a223 223 0 0 1-7.803-14.829a227 227 0 0 1 7.746-14.885a220 220 0 0 1 9.033-14.411c5.511-.417 11.192-.644 17.044-.625s11.363.227 16.836.663c3.143 4.583 6.154 9.469 8.995 14.298s5.473 9.98 7.821 14.923zm-62.134 28.18a178 178 0 0 1-14.847-2.766a173 173 0 0 1 5.018-14.298a255 255 0 0 0 4.678 8.541c1.628 2.86 3.257 5.663 5.15 8.522m-5.284-47.345a172 172 0 0 0-4.658 8.56a191 191 0 0 1-5.208-14.43a167 167 0 0 1 15.017-2.766c-1.761 2.803-3.446 5.7-5.15 8.636m33.86 69.785c-3.408-3.636-6.76-7.575-10.055-11.798c3.257.152 6.571.227 9.923.227s6.78 0 10.094-.208a175 175 0 0 1-9.961 11.78m42.005 22.726a9.13 9.13 0 0 1-4.697 1.117c-3.447 0-13.256-1.515-28.71-15.34l-2.215-2.026a186 186 0 0 0 15.434-18.938a184 184 0 0 0 23.975-3.9c.833 3.673 1.496 7.271 1.894 10.737c1.894 14.279-.284 25.187-5.681 28.274zm9.999-95.939a111 111 0 0 1 10.491 3.788c13.427 5.681 21.779 12.972 21.76 19.203c-.02 6.23-8.352 13.559-21.798 19.146a115 115 0 0 1-10.302 3.655a187 187 0 0 0-8.654-22.99a186 186 0 0 0 8.503-22.878zm-43.557-36.152c15.51-13.881 25.206-15.377 28.653-15.377a8.9 8.9 0 0 1 4.583 1.098c5.416 3.087 7.575 13.976 5.851 28.407a107 107 0 0 1-1.893 10.965a185 185 0 0 0-23.824-3.92a186.5 186.5 0 0 0-15.605-19.09a30 30 0 0 1 2.254-2.159zm-8.617 8.446a166 166 0 0 1 10.037 11.799a242 242 0 0 0-9.904-.228c-3.352 0-6.78 0-10.094.228c3.239-4.205 6.553-8.163 9.961-11.799M98.344 31.588a9.13 9.13 0 0 1 4.677-1.06c3.412.142 6.758.986 9.829 2.48a74 74 0 0 1 18.938 12.84l2.215 2.027a186.5 186.5 0 0 0-15.453 18.937a187 187 0 0 0-23.956 3.901c-.833-3.673-1.515-7.272-1.894-10.737c-1.97-14.298.303-25.206 5.625-28.312zM77.853 85.542a109 109 0 0 1 10.302-3.674a185.5 185.5 0 0 0 8.636 22.99a187 187 0 0 0-8.503 22.725a111 111 0 0 1-10.492-3.787c-13.446-5.681-21.778-12.973-21.778-19.203s8.408-13.465 21.835-19.051m62.494 1.893c-9.601-.01-17.393 7.765-17.403 17.366c-.01 9.602 7.764 17.394 17.366 17.404c9.6.01 17.393-7.764 17.403-17.366a17.366 17.366 0 0 0-17.366-17.498zm0 0c-9.601-.01-17.393 7.765-17.403 17.366c-.01 9.602 7.764 17.394 17.366 17.404c9.6.01 17.393-7.764 17.403-17.366a17.366 17.366 0 0 0-17.366-17.498zm0 0c-9.601-.01-17.393 7.765-17.403 17.366c-.01 9.602 7.764 17.394 17.366 17.404c9.6.01 17.393-7.764 17.403-17.366a17.366 17.366 0 0 0-17.366-17.498zm93.458 17.499c0-13.048-15.358-24.62-38.974-31.683c5.682-23.994 3.2-43.083-8.105-49.541a18.1 18.1 0 0 0-9.071-2.273c-10.549 0-23.88 7.367-37.345 20.112C126.845 28.9 113.55 21.57 103.02 21.57a17.9 17.9 0 0 0-9.204 2.31c-11.249 6.477-13.578 25.453-7.972 49.333c-23.502 7.026-38.785 18.502-38.823 31.494c-.038 12.99 15.378 24.619 38.993 31.663c-5.681 24.013-3.22 43.084 8.105 49.542a17.8 17.8 0 0 0 9.053 2.272c10.567 0 23.9-7.367 37.364-20.112c13.446 12.65 26.74 19.98 37.288 19.98a18.2 18.2 0 0 0 9.204-2.31c11.249-6.478 13.56-25.453 7.973-49.239c23.501-7.158 38.784-18.635 38.803-31.607zM148.983 45.79c15.51-13.881 25.206-15.377 28.653-15.377a8.9 8.9 0 0 1 4.583 1.098c5.416 3.087 7.575 13.976 5.851 28.407a107 107 0 0 1-1.893 10.965a185 185 0 0 0-23.824-3.92a186.5 186.5 0 0 0-15.605-19.09a48 48 0 0 1 2.235-2.083m-46.947 69.843a255 255 0 0 0 4.678 8.54c1.629 2.86 3.333 5.758 5.227 8.617a178 178 0 0 1-14.847-2.765c1.325-4.81 3.01-9.544 4.942-14.392m-5.245-36.058a167 167 0 0 1 15.017-2.765c-1.894 2.803-3.522 5.682-5.227 8.693c-1.704 3.01-3.2 5.681-4.658 8.56a223 223 0 0 1-5.132-14.488m9.866 25.263a227 227 0 0 1 7.746-14.885a220 220 0 0 1 9.033-14.411c5.51-.417 11.192-.644 17.044-.625s11.363.227 16.836.662c3.143 4.583 6.154 9.47 8.995 14.298c2.84 4.83 5.454 9.886 7.802 14.829a214 214 0 0 1-7.726 14.866c-2.803 4.924-5.89 9.81-9.034 14.43c-5.51.436-11.192.644-17.043.644a210 210 0 0 1-16.836-.682a213 213 0 0 1-9.014-14.298c-2.841-4.942-5.36-9.885-7.727-14.828zm62.23-27.97c5.15.719 10.131 1.647 14.847 2.745a170 170 0 0 1-5 14.298a256 256 0 0 0-4.696-8.54c-1.648-2.86-3.39-5.739-5.076-8.504zm5.226 47.192a250 250 0 0 0 4.659-8.579c1.894 4.924 3.788 9.772 5.113 14.506c-4.772 1.118-9.79 2.027-14.998 2.746a172 172 0 0 0 5.302-8.673zm-33.766-69.748a166 166 0 0 1 10.037 11.799a242 242 0 0 0-9.904-.228c-3.352 0-6.78 0-10.094.228c3.239-4.205 6.553-8.163 9.961-11.799M98.344 31.588a9.13 9.13 0 0 1 4.677-1.06c3.412.142 6.758.986 9.829 2.48a74 74 0 0 1 18.938 12.84l2.215 2.027a186.5 186.5 0 0 0-15.453 18.937a187 187 0 0 0-23.956 3.901c-.833-3.673-1.515-7.272-1.894-10.737c-1.97-14.298.303-25.206 5.625-28.312zm-10.019 96.014a111 111 0 0 1-10.491-3.787c-13.446-5.682-21.778-12.973-21.778-19.203s8.37-13.484 21.797-19.07a109 109 0 0 1 10.302-3.674a185.5 185.5 0 0 0 8.636 22.99a187 187 0 0 0-8.466 22.744m43.557 36.171c-15.51 13.882-25.206 15.359-28.671 15.359a8.9 8.9 0 0 1-4.583-1.099c-5.417-3.086-7.575-13.976-5.852-28.406c.44-3.68 1.072-7.333 1.894-10.946a188.5 188.5 0 0 0 23.823 3.901a184 184 0 0 0 15.605 19.09zm8.655-8.54c-3.409-3.637-6.761-7.576-10.056-11.799c3.257.152 6.571.227 9.923.227s6.78 0 10.094-.208c-3.276 4.204-6.61 8.143-9.98 11.78zm42.004 22.725a9.13 9.13 0 0 1-4.697 1.117c-3.447 0-13.256-1.515-28.71-15.34l-2.215-2.026a186 186 0 0 0 15.434-18.938a184 184 0 0 0 23.975-3.9c.833 3.673 1.496 7.271 1.894 10.737c1.875 14.279-.303 25.187-5.7 28.274zm20.452-53.86a115 115 0 0 1-10.302 3.656a187 187 0 0 0-8.654-22.99a186 186 0 0 0 8.503-22.726a111 111 0 0 1 10.491 3.788c13.427 5.681 21.779 12.972 21.76 19.203c-.02 6.23-8.37 13.407-21.817 18.994zm-62.646-1.93c9.602.01 17.394-7.765 17.404-17.367s-7.764-17.393-17.366-17.403s-17.393 7.764-17.404 17.365a17.366 17.366 0 0 0 17.366 17.404M9.071 225.377V15.756L0 24.846v209.565h231.135l9.052-9.033z" />
  </svg>
  <svg v-if="name === 'flutter'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="25.85" height="32"
    viewBox="0 0 256 317">
    <defs>
      <linearGradient id="logosFlutter0" x1="3.952%" x2="75.897%" y1="26.993%" y2="52.919%">
        <stop offset="0%" />
        <stop offset="100%" stop-opacity="0" />
      </linearGradient>
    </defs>
    <path fill="#47C5FB"
      d="M157.666.001L.001 157.666l48.8 48.8L255.268.001zm-1.099 145.396l-84.418 84.418l48.984 49.716l48.71-48.71l85.425-85.424z" />
    <path fill="#00569E" d="m121.133 279.531l37.082 37.082h97.052l-85.425-85.792z" />
    <path fill="#00B5F8" d="m71.6 230.364l48.801-48.801l49.441 49.258l-48.709 48.71z" />
    <path fill="url(#logosFlutter0)" fill-opacity=".8" d="m121.133 279.531l40.56-13.459l4.029-31.131z" />
  </svg>
  <svg v-if="name === 'nest'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="32.13" height="32"
    viewBox="0 0 256 255">
    <path fill="#E0234E"
      d="M150.736 0c-1.851 0-3.57.397-5.157.926c3.372 2.247 5.223 5.222 6.148 8.594c.067.463.199.794.265 1.256c.066.397.132.794.132 1.19c.265 5.818-1.52 6.545-2.777 9.983c-1.917 4.43-1.388 9.19.926 13.024c.198.463.463.992.793 1.455c-2.512-16.727 11.438-19.239 14.016-24.462c.198-4.561-3.57-7.603-6.545-9.718C155.694.528 153.116 0 150.736 0m21.023 3.768c-.264 1.521-.066 1.124-.132 1.918c-.066.529-.066 1.19-.132 1.719c-.132.528-.264 1.057-.463 1.586c-.132.53-.33 1.058-.528 1.587c-.265.529-.463.992-.728 1.52c-.198.265-.33.53-.529.794l-.396.595c-.33.463-.661.926-.992 1.322c-.397.397-.727.86-1.19 1.19v.066c-.397.331-.793.728-1.256 1.058c-1.388 1.058-2.975 1.851-4.43 2.843c-.462.33-.925.595-1.322.992c-.463.33-.86.66-1.256 1.058c-.463.396-.793.793-1.19 1.256c-.33.396-.727.86-.992 1.322c-.33.463-.66.925-.925 1.388c-.265.53-.463.992-.728 1.52c-.198.53-.396.993-.528 1.521c-.199.595-.331 1.124-.463 1.653c-.066.265-.066.595-.132.86c-.067.264-.067.529-.132.793c0 .529-.067 1.124-.067 1.653c0 .397 0 .793.067 1.19c0 .529.066 1.058.198 1.653c.066.529.198 1.057.33 1.586c.199.53.331 1.058.53 1.587c.131.33.33.661.462.926L139.63 35.04a163 163 0 0 0-7.669-1.984l-4.165-.991c-3.967-.794-8-1.389-12.032-1.785c-.132 0-.199-.067-.33-.067a118 118 0 0 0-11.835-.595c-2.909 0-5.818.132-8.66.33c-4.034.265-8.066.794-12.1 1.455c-.991.133-1.983.331-2.974.53c-2.05.396-4.033.859-5.95 1.322l-2.976.793c-.991.397-1.917.86-2.842 1.256l-2.182.992c-.132.066-.265.066-.33.132c-.662.33-1.257.595-1.852.925a3 3 0 0 0-.463.199c-.727.33-1.454.727-2.049 1.058c-.463.198-.926.462-1.322.66a6 6 0 0 1-.595.331c-.595.33-1.19.661-1.72.992q-.891.495-1.586.992c-.463.33-.925.595-1.322.925c-.066.066-.132.066-.199.132c-.396.265-.859.595-1.256.926l-.132.132l-.991.793c-.133.067-.265.199-.397.265c-.33.264-.661.595-.992.86c-.066.131-.198.198-.264.264c-.397.397-.794.727-1.19 1.124c-.067 0-.067.066-.132.132c-.397.33-.794.727-1.19 1.124c-.067.066-.067.132-.133.132c-.33.33-.66.661-.991 1.058c-.132.132-.33.264-.463.396c-.33.397-.727.794-1.124 1.19c-.066.133-.198.199-.265.331c-.528.529-.991 1.058-1.52 1.587l-.198.198c-1.058 1.124-2.182 2.248-3.372 3.24a38 38 0 0 1-3.703 2.909a50 50 0 0 1-3.966 2.512a40 40 0 0 1-4.165 1.983a48 48 0 0 1-4.298 1.587c-2.776.595-5.62 1.719-8.065 1.917c-.53 0-1.124.132-1.653.198a67 67 0 0 0-1.653.397l-1.587.595c-.529.198-1.057.463-1.586.727c-.463.33-.992.595-1.455.926c-.463.33-.925.727-1.322 1.124c-.463.33-.926.793-1.322 1.19c-.397.463-.794.86-1.124 1.322c-.33.529-.727.992-.992 1.52c-.33.464-.661.992-.926 1.521c-.264.595-.528 1.124-.727 1.72a51 51 0 0 0-.595 1.718c-.132.529-.264 1.058-.33 1.587c0 .066-.067.132-.067.198c-.132.595-.132 1.389-.198 1.785c-.066.463-.132.86-.132 1.322c0 .265 0 .595.066.86c.066.463.132.86.265 1.256c.132.397.264.793.463 1.19v.066c.198.397.462.794.727 1.19c.264.397.529.794.86 1.19c.33.33.726.728 1.123 1.058c.397.397.794.727 1.256 1.058c1.587 1.388 1.984 1.851 4.033 2.909c.33.198.661.33 1.058.529c.066 0 .132.066.198.066c0 .132 0 .198.067.33c.066.53.198 1.058.33 1.587c.132.595.33 1.124.529 1.587c.198.397.33.793.529 1.19c.066.132.132.265.198.33c.265.53.53.992.794 1.455l.991 1.388c.33.397.727.86 1.124 1.257s.794.727 1.256 1.123c0 0 .067.067.132.067c.397.33.794.66 1.19.925c.463.33.926.595 1.455.86c.463.264.992.529 1.52.727c.397.198.86.33 1.323.463c.066.066.132.066.264.132c.265.066.595.132.86.198c-.199 3.57-.265 6.942.264 8.132c.595 1.322 3.504-2.71 6.413-7.338c-.396 4.561-.66 9.916 0 11.503c.727 1.653 4.694-3.504 8.132-9.19c46.874-10.842 89.648 21.553 94.144 67.303c-.86-7.14-9.652-11.107-13.685-10.115c-1.984 4.892-5.355 11.173-10.776 15.073c.462-4.363.264-8.859-.662-13.222c-1.454 6.082-4.297 11.768-8.198 16.66c-6.28.463-12.56-2.578-15.867-7.14c-.264-.198-.33-.595-.528-.86c-.199-.462-.397-.925-.53-1.388a5.5 5.5 0 0 1-.396-1.388c-.066-.463-.066-.926-.066-1.455v-.991c.066-.463.198-.926.33-1.389s.265-.925.463-1.388c.265-.463.463-.926.793-1.388c1.124-3.174 1.124-5.752-.925-7.273a7.5 7.5 0 0 0-1.256-.66c-.265-.067-.595-.2-.86-.265c-.198-.067-.33-.133-.529-.199c-.462-.132-.925-.264-1.388-.33a5 5 0 0 0-1.388-.199c-.463-.066-.992-.132-1.455-.132c-.33 0-.66.066-.992.066c-.528 0-.991.067-1.454.199c-.463.066-.926.132-1.388.264c-.463.132-.926.265-1.389.463s-.86.397-1.322.595c-.397.198-.793.463-1.256.661c-15.404 10.05-6.215 33.585 4.297 40.395c-3.967.727-8 1.587-9.123 2.446l-.132.132c2.842 1.72 5.817 3.174 8.925 4.43c4.231 1.388 8.727 2.644 10.71 3.173v.066a64 64 0 0 0 16.66 1.19c29.288-2.05 53.287-24.329 57.65-53.683c.132.595.265 1.124.397 1.719c.198 1.19.463 2.446.595 3.702v.067c.132.595.198 1.19.265 1.719v.264c.066.595.132 1.19.132 1.719c.066.727.132 1.454.132 2.182v1.057c0 .331.066.728.066 1.058c0 .397-.066.794-.066 1.19v.926c0 .463-.066.86-.066 1.322c0 .265 0 .529-.067.86c0 .462-.066.925-.066 1.454c-.066.198-.066.397-.066.595c-.066.529-.132.992-.199 1.52c0 .199 0 .397-.066.596c-.066.66-.198 1.256-.264 1.917v.132c-.132.595-.265 1.257-.397 1.852v.198l-.397 1.785c0 .066-.066.198-.066.264c-.132.595-.264 1.19-.463 1.786v.198c-.198.661-.396 1.256-.528 1.851c-.067.066-.067.132-.067.132l-.595 1.983c-.264.662-.462 1.257-.727 1.918c-.264.66-.463 1.322-.727 1.917c-.265.661-.53 1.256-.793 1.917h-.067c-.264.595-.529 1.256-.86 1.851a3 3 0 0 1-.198.463c-.066.067-.066.132-.132.199c-4.297 8.66-10.644 16.263-18.577 22.213c-.53.33-1.058.728-1.587 1.124c-.132.132-.33.199-.463.33c-.463.331-.925.662-1.454.992l.198.397h.066l2.777-.397h.066c1.72-.264 3.438-.595 5.157-.925c.463-.066.992-.198 1.454-.33c.331-.067.595-.133.926-.199c.463-.066.926-.198 1.388-.265c.397-.132.794-.198 1.19-.33c6.612-1.587 13.025-3.769 19.173-6.347c-10.512 14.346-24.594 25.916-41.056 33.519c7.603-.529 15.206-1.785 22.545-3.9c26.643-7.868 49.055-25.784 62.476-49.915a105.5 105.5 0 0 1-17.785 42.51a104 104 0 0 0 17.652-14.677c14.81-15.47 24.528-35.106 27.834-56.196a105.3 105.3 0 0 1 1.917 31.867c47.733-66.576 3.967-135.597-14.346-153.778c-.067-.132-.132-.198-.132-.33c-.067.066-.067.066-.067.132c0-.067 0-.067-.066-.132c0 .793-.066 1.586-.132 2.38c-.198 1.52-.397 2.975-.661 4.43c-.33 1.454-.727 2.908-1.124 4.363a55 55 0 0 1-1.587 4.23a57 57 0 0 1-1.983 4.034c-.727 1.256-1.52 2.578-2.38 3.768a51 51 0 0 1-2.71 3.57c-.993 1.19-2.05 2.248-3.108 3.306a43 43 0 0 1-1.917 1.652c-.53.463-.992.86-1.521 1.323c-1.19.925-2.38 1.785-3.702 2.578c-1.256.793-2.579 1.587-3.9 2.248c-1.39.661-2.778 1.256-4.166 1.851a47 47 0 0 1-4.297 1.388a53 53 0 0 1-4.43.992c-1.52.265-3.04.397-4.495.529c-1.058.066-2.116.132-3.174.132c-1.52 0-3.041-.132-4.495-.264c-1.521-.133-3.042-.331-4.496-.662c-1.52-.264-2.975-.66-4.43-1.123h-.066c1.455-.133 2.91-.265 4.364-.53a48 48 0 0 0 4.43-.991a47 47 0 0 0 4.296-1.388c1.455-.53 2.843-1.19 4.166-1.852c1.388-.66 2.644-1.388 3.966-2.181c1.256-.86 2.513-1.72 3.703-2.645a36 36 0 0 0 3.371-2.975c1.124-.992 2.116-2.115 3.108-3.24a65 65 0 0 0 2.776-3.57c.132-.198.265-.462.397-.66c.661-1.058 1.322-2.116 1.917-3.174a46 46 0 0 0 1.984-4.033a46 46 0 0 0 1.586-4.23c.463-1.39.794-2.844 1.124-4.298c.265-1.52.53-2.975.661-4.43c.132-1.52.265-3.04.265-4.495c0-1.058-.066-2.116-.132-3.174c-.132-1.52-.33-2.975-.53-4.43a48 48 0 0 0-.99-4.429c-.464-1.388-.926-2.843-1.455-4.231c-.53-1.388-1.19-2.777-1.851-4.099c-.728-1.322-1.455-2.645-2.248-3.9a73 73 0 0 0-2.645-3.637a140 140 0 0 0-3.041-3.372a41 41 0 0 0-1.719-1.652a122 122 0 0 0-9.19-6.48a12 12 0 0 0-1.322-.66c-2.181-1.389-4.23-2.116-6.28-2.777" />
  </svg>
  <svg v-if="name === 'node'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="28.35" height="32"
    viewBox="0 0 256 289">
    <path fill="#539E43"
      d="M128 288.464c-3.975 0-7.685-1.06-11.13-2.915l-35.247-20.936c-5.3-2.915-2.65-3.975-1.06-4.505c7.155-2.385 8.48-2.915 15.9-7.156c.796-.53 1.856-.265 2.65.265l27.032 16.166c1.06.53 2.385.53 3.18 0l105.74-61.217c1.06-.53 1.59-1.59 1.59-2.915V83.08c0-1.325-.53-2.385-1.59-2.915l-105.74-60.953c-1.06-.53-2.385-.53-3.18 0L20.405 80.166c-1.06.53-1.59 1.855-1.59 2.915v122.17c0 1.06.53 2.385 1.59 2.915l28.887 16.695c15.636 7.95 25.44-1.325 25.44-10.6V93.68c0-1.59 1.326-3.18 3.181-3.18h13.516c1.59 0 3.18 1.325 3.18 3.18v120.58c0 20.936-11.396 33.126-31.272 33.126c-6.095 0-10.865 0-24.38-6.625l-27.827-15.9C4.24 220.885 0 213.465 0 205.515V83.346C0 75.396 4.24 67.976 11.13 64L116.87 2.783c6.625-3.71 15.635-3.71 22.26 0L244.87 64C251.76 67.975 256 75.395 256 83.346v122.17c0 7.95-4.24 15.37-11.13 19.345L139.13 286.08c-3.445 1.59-7.42 2.385-11.13 2.385m32.596-84.009c-46.377 0-55.917-21.2-55.917-39.221c0-1.59 1.325-3.18 3.18-3.18h13.78c1.59 0 2.916 1.06 2.916 2.65c2.12 14.045 8.215 20.936 36.306 20.936c22.261 0 31.802-5.035 31.802-16.96c0-6.891-2.65-11.926-37.367-15.372c-28.886-2.915-46.907-9.275-46.907-32.33c0-21.467 18.02-34.187 48.232-34.187c33.921 0 50.617 11.66 52.737 37.101q0 1.193-.795 2.385c-.53.53-1.325 1.06-2.12 1.06h-13.78c-1.326 0-2.65-1.06-2.916-2.385c-3.18-14.575-11.395-19.345-33.126-19.345c-24.38 0-27.296 8.48-27.296 14.84c0 7.686 3.445 10.07 36.306 14.31c32.597 4.24 47.967 10.336 47.967 33.127c-.265 23.321-19.345 36.571-53.002 36.571" />
  </svg>
  <svg v-if="name === 'deno'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="32" height="32"
    viewBox="0 0 256 256">
    <path d="M128 0c70.692 0 128 57.308 128 128s-57.308 128-128 128S0 198.692 0 128S57.308 0 128 0" />
    <path fill="#FFF"
      d="M123 72.45c17.9 0 33.25 4.95 45.35 14.4c10.184 7.96 17.68 18.9 21.617 31.278l.133.422l.1.3l.1.35l.2.65l.3 1.2l.8 2.8l.85 3.2l3.75 13.7l3.7 13.8l4.2 15.7l6.75 25.25l2.55 9.5l-.55.6c-17.622 19.256-41.713 32.483-68.827 36.288l-.823.112l-.25-1.65l-.45-3.3l-.4-2.4l-.45-3.15l-.6-3.85l-.25-1.5l-.55-3.65l-.35-2.15l-.45-2.8l-.45-2.7l-.45-2.6l-.45-2.55l-.4-2.5l-.45-2.4l-.4-2.3l-.3-1.7l-.35-1.65l-.6-3.15l-.3-1.5l-.35-1.85l-.3-1.3l-.25-1.25l-.25-1.2l-.15-.8l-.35-1.5l-.5-2.2l-.2-.7l-.25-1l-.2-.95l-.25-.95l-.25-.9l-.15-.55l-.25-.85l-.2-.8l-.15-.55l-.2-.5l-.15-.45l-.2-.7l-.15-.5l-.1-.3a33 33 0 0 0-.706-1.886l-.194-.464l-.15-.3l1.15-3l-4.55.15l-1.25.05c-41.3.85-67.95-16.7-67.95-44.2c0-29.15 29-52.6 66.2-52.6m-40.3 137.7c.935-3.2 4.246-5.091 7.45-4.29l.15.04c3.25.837 5.19 4.098 4.485 7.348l-.035.152l-.05.15l-6.05 22.55l-.85-.35a114 114 0 0 1-9.94-4.253l-.81-.397l5.6-20.8zm34.8-15.25c.95-3.25 4.35-5.15 7.65-4.25a6.25 6.25 0 0 1 3.815 2.991l.085.159l.2.9l.3 1.45l.2 1.05l-.05.25l-.15.7v.15l-8.5 31.5l-.05.15a6.25 6.25 0 0 1-12.08-3.098l.03-.152v-.15l8.5-31.5zm-51.6-36.7c3.024 2.64 6.509 5.05 10.322 7.096l.478.254l-7.7 28.6l-.05.15a6.25 6.25 0 0 1-12.084-3.149L56.9 191l.05-.15l8.5-31.5l.05-.2zm-27.9-32c.95-3.2 4.35-5.1 7.65-4.25c3.2.886 5.14 4.148 4.435 7.398l-.035.152v.15l-8.5 31.5l-.05.15a6.25 6.25 0 0 1-12.084-3.149l.034-.151v-.15l8.5-31.5zm190.6-7.15c.95-3.2 4.35-5.1 7.6-4.25c3.25.886 5.19 4.148 4.485 7.35l-.035.15v.2L232.1 154v.15a6.271 6.271 0 0 1-12.139-3.15l.039-.15l.05-.15l8.5-31.5zM27.1 72.75l.217.007q.651.03 1.283.193a6.28 6.28 0 0 1 4.485 7.398l-.035.152l-.05.15l-8.5 31.5l-.05.15c-.95 3.2-4.35 5.1-7.6 4.25a6.2 6.2 0 0 1-3.1-1.9a114.2 114.2 0 0 1 13.033-41.318zm179.95 4.45c1-3.2 4.35-5.1 7.65-4.25c3.2.886 5.188 4.148 4.485 7.398l-.035.152l-.05.15l-8.5 31.5l-.05.15a6.25 6.25 0 0 1-12.084-3.149l.034-.151l.05-.15l8.5-31.5zM56.5 47.25c.95-3.2 4.35-5.1 7.6-4.25c3.25.886 5.19 4.148 4.485 7.35l-.035.15l-.05.2L60 82.2l-.05.15a6.25 6.25 0 0 1-12.084-3.149l.034-.151l.05-.15l8.5-31.5zm109.2 5.95c.95-3.2 4.35-5.1 7.65-4.25c3.2.886 5.14 4.148 4.435 7.398l-.035.152v.15l-6.7 24.75l-.55-.45a70 70 0 0 0-9.935-6.094l-.615-.306l5.7-21.2zm-47.95-39.75a6.2 6.2 0 0 1-.004 2.603l-.046.197l-.05.15l-8.5 31.5l-.05.15a6.25 6.25 0 0 1-12.08-3.098l.03-.152l.05-.15L105 15.3l.85-.15a115 115 0 0 1 11.9-1.7m78.2 21.75l.75.55a116 116 0 0 1 9.222 7.666l.628.584l-.2.65l-.05.15a6.25 6.25 0 0 1-12.08-3.098l.03-.152l.05-.15zM146.5 14.5l.9.15a114 114 0 0 1 10.44 2.267l.86.233l-3.15 11.75l-.05.15a6.25 6.25 0 0 1-12.08-3.098l.03-.152l.05-.15z" />
    <path d="M131 93.5a8 8 0 1 1 0 16a8 8 0 0 1 0-16" />
  </svg>
  <svg v-if="name === 'mongo'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="14.93" height="32"
    viewBox="0 0 256 549">
    <path fill="#01EC64"
      d="M175.622 61.108C152.612 33.807 132.797 6.078 128.749.32a1.03 1.03 0 0 0-1.492 0c-4.048 5.759-23.863 33.487-46.874 60.788c-197.507 251.896 31.108 421.89 31.108 421.89l1.917 1.28c1.704 26.234 5.966 63.988 5.966 63.988h17.045s4.26-37.54 5.965-63.987l1.918-1.494c.213.214 228.828-169.78 31.32-421.677m-47.726 418.05s-10.227-8.744-12.997-13.222v-.428l12.358-274.292c0-.853 1.279-.853 1.279 0l12.357 274.292v.428c-2.77 4.478-12.997 13.223-12.997 13.223" />
  </svg>
  <svg v-if="name === 'postgres'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="31.04" height="32"
    viewBox="0 0 256 264">
    <path
      d="M255.008 158.086c-1.535-4.649-5.556-7.887-10.756-8.664c-2.452-.366-5.26-.21-8.583.475c-5.792 1.195-10.089 1.65-13.225 1.738c11.837-19.985 21.462-42.775 27.003-64.228c8.96-34.689 4.172-50.492-1.423-57.64C233.217 10.847 211.614.683 185.552.372c-13.903-.17-26.108 2.575-32.475 4.549c-5.928-1.046-12.302-1.63-18.99-1.738c-12.537-.2-23.614 2.533-33.079 8.15c-5.24-1.772-13.65-4.27-23.362-5.864c-22.842-3.75-41.252-.828-54.718 8.685C6.622 25.672-.937 45.684.461 73.634c.444 8.874 5.408 35.874 13.224 61.48c4.492 14.718 9.282 26.94 14.237 36.33c7.027 13.315 14.546 21.156 22.987 23.972c4.731 1.576 13.327 2.68 22.368-4.85c1.146 1.388 2.675 2.767 4.704 4.048c2.577 1.625 5.728 2.953 8.875 3.74c11.341 2.835 21.964 2.126 31.027-1.848c.056 1.612.099 3.152.135 4.482c.06 2.157.12 4.272.199 6.25c.537 13.374 1.447 23.773 4.143 31.049c.148.4.347 1.01.557 1.657c1.345 4.118 3.594 11.012 9.316 16.411c5.925 5.593 13.092 7.308 19.656 7.308c3.292 0 6.433-.432 9.188-1.022c9.82-2.105 20.973-5.311 29.041-16.799c7.628-10.86 11.336-27.217 12.007-52.99q.13-1.094.244-2.088l.16-1.362l1.797.158l.463.031c10.002.456 22.232-1.665 29.743-5.154c5.935-2.754 24.954-12.795 20.476-26.351" />
    <path fill="#336791"
      d="M237.906 160.722c-29.74 6.135-31.785-3.934-31.785-3.934c31.4-46.593 44.527-105.736 33.2-120.211c-30.904-39.485-84.399-20.811-85.292-20.327l-.287.052c-5.876-1.22-12.451-1.946-19.842-2.067c-13.456-.22-23.664 3.528-31.41 9.402c0 0-95.43-39.314-90.991 49.444c.944 18.882 27.064 142.873 58.218 105.422c11.387-13.695 22.39-25.274 22.39-25.274c5.464 3.63 12.006 5.482 18.864 4.817l.533-.452c-.166 1.7-.09 3.363.213 5.332c-8.026 8.967-5.667 10.541-21.711 13.844c-16.235 3.346-6.698 9.302-.471 10.86c7.549 1.887 25.013 4.561 36.813-11.958l-.47 1.885c3.144 2.519 5.352 16.383 4.982 28.952c-.37 12.568-.617 21.197 1.86 27.937c2.479 6.74 4.948 21.905 26.04 17.386c17.623-3.777 26.756-13.564 28.027-29.89c.901-11.606 2.942-9.89 3.07-20.267l1.637-4.912c1.887-15.733.3-20.809 11.157-18.448l2.64.232c7.99.363 18.45-1.286 24.589-4.139c13.218-6.134 21.058-16.377 8.024-13.686z" />
    <path fill="#FFF"
      d="M108.076 81.525c-2.68-.373-5.107-.028-6.335.902c-.69.523-.904 1.129-.962 1.546c-.154 1.105.62 2.327 1.096 2.957c1.346 1.784 3.312 3.01 5.258 3.28q.423.059.842.058c3.245 0 6.196-2.527 6.456-4.392c.325-2.336-3.066-3.893-6.355-4.35m88.784.073c-.256-1.831-3.514-2.353-6.606-1.923c-3.088.43-6.082 1.824-5.832 3.659c.2 1.427 2.777 3.863 5.827 3.863q.387 0 .78-.054c2.036-.282 3.53-1.575 4.24-2.32c1.08-1.136 1.706-2.402 1.591-3.225" />
    <path fill="#FFF"
      d="M247.802 160.025c-1.134-3.429-4.784-4.532-10.848-3.28c-18.005 3.716-24.453 1.142-26.57-.417c13.995-21.32 25.508-47.092 31.719-71.137c2.942-11.39 4.567-21.968 4.7-30.59c.147-9.463-1.465-16.417-4.789-20.665c-13.402-17.125-33.072-26.311-56.882-26.563c-16.369-.184-30.199 4.005-32.88 5.183c-5.646-1.404-11.801-2.266-18.502-2.376c-12.288-.199-22.91 2.743-31.704 8.74c-3.82-1.422-13.692-4.811-25.765-6.756c-20.872-3.36-37.458-.814-49.294 7.571c-14.123 10.006-20.643 27.892-19.38 53.16c.425 8.501 5.269 34.653 12.913 59.698c10.062 32.964 21 51.625 32.508 55.464c1.347.449 2.9.763 4.613.763c4.198 0 9.345-1.892 14.7-8.33a530 530 0 0 1 20.261-22.926c4.524 2.428 9.494 3.784 14.577 3.92q.016.2.035.398a118 118 0 0 0-2.57 3.175c-3.522 4.471-4.255 5.402-15.592 7.736c-3.225.666-11.79 2.431-11.916 8.435c-.136 6.56 10.125 9.315 11.294 9.607c4.074 1.02 7.999 1.523 11.742 1.523c9.103 0 17.114-2.992 23.516-8.781c-.197 23.386.778 46.43 3.586 53.451c2.3 5.748 7.918 19.795 25.664 19.794c2.604 0 5.47-.303 8.623-.979c18.521-3.97 26.564-12.156 29.675-30.203c1.665-9.645 4.522-32.676 5.866-45.03c2.836.885 6.487 1.29 10.434 1.289c8.232 0 17.731-1.749 23.688-4.514c6.692-3.108 18.768-10.734 16.578-17.36m-44.106-83.48c-.061 3.647-.563 6.958-1.095 10.414c-.573 3.717-1.165 7.56-1.314 12.225c-.147 4.54.42 9.26.968 13.825c1.108 9.22 2.245 18.712-2.156 28.078a37 37 0 0 1-1.95-4.009c-.547-1.326-1.735-3.456-3.38-6.404c-6.399-11.476-21.384-38.35-13.713-49.316c2.285-3.264 8.084-6.62 22.64-4.813m-17.644-61.787c21.334.471 38.21 8.452 50.158 23.72c9.164 11.711-.927 64.998-30.14 110.969a171 171 0 0 0-.886-1.117l-.37-.462c7.549-12.467 6.073-24.802 4.759-35.738c-.54-4.488-1.05-8.727-.92-12.709c.134-4.22.692-7.84 1.232-11.34c.663-4.313 1.338-8.776 1.152-14.037c.139-.552.195-1.204.122-1.978c-.475-5.045-6.235-20.144-17.975-33.81c-6.422-7.475-15.787-15.84-28.574-21.482c5.5-1.14 13.021-2.203 21.442-2.016M66.674 175.778c-5.9 7.094-9.974 5.734-11.314 5.288c-8.73-2.912-18.86-21.364-27.791-50.624c-7.728-25.318-12.244-50.777-12.602-57.916c-1.128-22.578 4.345-38.313 16.268-46.769c19.404-13.76 51.306-5.524 64.125-1.347c-.184.182-.376.352-.558.537c-21.036 21.244-20.537 57.54-20.485 59.759c-.002.856.07 2.068.168 3.735c.362 6.105 1.036 17.467-.764 30.334c-1.672 11.957 2.014 23.66 10.111 32.109a36 36 0 0 0 2.617 2.468c-3.604 3.86-11.437 12.396-19.775 22.426m22.479-29.993c-6.526-6.81-9.49-16.282-8.133-25.99c1.9-13.592 1.199-25.43.822-31.79c-.053-.89-.1-1.67-.127-2.285c3.073-2.725 17.314-10.355 27.47-8.028c4.634 1.061 7.458 4.217 8.632 9.645c6.076 28.103.804 39.816-3.432 49.229c-.873 1.939-1.698 3.772-2.402 5.668l-.546 1.466c-1.382 3.706-2.668 7.152-3.465 10.424c-6.938-.02-13.687-2.984-18.819-8.34m1.065 37.9c-2.026-.506-3.848-1.385-4.917-2.114c.893-.42 2.482-.992 5.238-1.56c13.337-2.745 15.397-4.683 19.895-10.394c1.031-1.31 2.2-2.794 3.819-4.602l.002-.002c2.411-2.7 3.514-2.242 5.514-1.412c1.621.67 3.2 2.702 3.84 4.938c.303 1.056.643 3.06-.47 4.62c-9.396 13.156-23.088 12.987-32.921 10.526m69.799 64.952c-16.316 3.496-22.093-4.829-25.9-14.346c-2.457-6.144-3.665-33.85-2.808-64.447c.011-.407-.047-.8-.159-1.17a15.4 15.4 0 0 0-.456-2.162c-1.274-4.452-4.379-8.176-8.104-9.72c-1.48-.613-4.196-1.738-7.46-.903c.696-2.868 1.903-6.107 3.212-9.614l.549-1.475c.618-1.663 1.394-3.386 2.214-5.21c4.433-9.848 10.504-23.337 3.915-53.81c-2.468-11.414-10.71-16.988-23.204-15.693c-7.49.775-14.343 3.797-17.761 5.53c-.735.372-1.407.732-2.035 1.082c.954-11.5 4.558-32.992 18.04-46.59c8.489-8.56 19.794-12.788 33.568-12.56c27.14.444 44.544 14.372 54.366 25.979c8.464 10.001 13.047 20.076 14.876 25.51c-13.755-1.399-23.11 1.316-27.852 8.096c-10.317 14.748 5.644 43.372 13.315 57.129c1.407 2.521 2.621 4.7 3.003 5.626c2.498 6.054 5.732 10.096 8.093 13.046c.724.904 1.426 1.781 1.96 2.547c-4.166 1.201-11.649 3.976-10.967 17.847c-.55 6.96-4.461 39.546-6.448 51.059c-2.623 15.21-8.22 20.875-23.957 24.25m68.104-77.936c-4.26 1.977-11.389 3.46-18.161 3.779c-7.48.35-11.288-.838-12.184-1.569c-.42-8.644 2.797-9.547 6.202-10.503c.535-.15 1.057-.297 1.561-.473q.469.383 1.032.756c6.012 3.968 16.735 4.396 31.874 1.271l.166-.033c-2.042 1.909-5.536 4.471-10.49 6.772" />
  </svg>
  <svg v-if="name === 'mysql'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="32.51" height="32"
    viewBox="0 0 256 252">
    <path fill="#00546B"
      d="M235.648 194.212c-13.918-.347-24.705 1.045-33.752 4.872c-2.61 1.043-6.786 1.044-7.134 4.35c1.392 1.392 1.566 3.654 2.784 5.567c2.09 3.479 5.741 8.177 9.047 10.614c3.653 2.783 7.308 5.566 11.134 8.002c6.786 4.176 14.442 6.611 21.053 10.787c3.829 2.434 7.654 5.568 11.482 8.177c1.914 1.39 3.131 3.654 5.568 4.523v-.521c-1.219-1.567-1.567-3.828-2.784-5.568c-1.738-1.74-3.48-3.306-5.22-5.046c-5.045-6.784-11.308-12.7-18.093-17.571c-5.567-3.828-17.747-9.047-20.008-15.485c0 0-.175-.173-.348-.347c3.827-.348 8.35-1.74 12.005-2.784c5.915-1.567 11.308-1.218 17.398-2.784c2.783-.696 5.567-1.566 8.35-2.436v-1.565c-3.13-3.132-5.392-7.307-8.698-10.265c-8.873-7.657-18.617-15.137-28.707-21.4c-5.394-3.48-12.354-5.742-18.095-8.699c-2.086-1.045-5.567-1.566-6.784-3.306c-3.133-3.827-4.873-8.872-7.134-13.396c-5.044-9.57-9.917-20.182-14.267-30.272c-3.13-6.786-5.044-13.572-8.872-19.834c-17.92-29.577-37.406-47.497-67.33-65.07c-6.438-3.653-14.093-5.219-22.27-7.132c-4.348-.175-8.699-.522-13.048-.697c-2.784-1.218-5.568-4.523-8.004-6.089C34.006 4.573 8.429-8.996 1.122 8.924c-4.698 11.308 6.96 22.442 10.96 28.185c2.96 4.001 6.786 8.524 8.874 13.048c1.218 2.956 1.565 6.09 2.783 9.221c2.785 7.653 5.393 16.18 9.048 23.314c1.914 3.653 4.001 7.48 6.437 10.786c1.392 1.913 3.827 2.784 4.35 5.915c-2.435 3.48-2.61 8.7-4.003 13.049c-6.263 19.66-3.826 44.017 5.046 58.457c2.783 4.348 9.395 13.92 18.268 10.265c7.83-3.131 6.09-13.048 8.35-21.747c.524-2.09.176-3.48 1.219-4.872v.349c2.436 4.87 4.871 9.569 7.133 14.44c5.394 8.524 14.788 17.398 22.617 23.314c4.177 3.13 7.482 8.524 12.702 10.438v-.523h-.349c-1.044-1.566-2.61-2.261-4.001-3.48c-3.131-3.13-6.612-6.958-9.047-10.438c-7.306-9.744-13.745-20.53-19.486-31.665c-2.783-5.392-5.22-11.308-7.481-16.701c-1.045-2.09-1.045-5.22-2.784-6.263c-2.61 3.827-6.437 7.133-8.351 11.83c-3.304 7.481-3.653 16.702-4.871 26.27c-.696.176-.349 0-.697.35c-5.566-1.394-7.48-7.134-9.569-12.006c-5.22-12.352-6.09-32.186-1.565-46.452c1.218-3.654 6.438-15.136 4.35-18.616c-1.044-3.306-4.525-5.22-6.438-7.829c-2.261-3.306-4.698-7.48-6.263-11.135c-4.176-9.743-6.264-20.53-10.787-30.273c-2.088-4.524-5.74-9.22-8.699-13.396c-3.305-4.697-6.959-8.004-9.569-13.571c-.869-1.913-2.088-5.045-.696-7.133c.348-1.392 1.043-1.913 2.436-2.261c2.262-1.915 8.7.521 10.96 1.565c6.438 2.608 11.831 5.046 17.225 8.699c2.435 1.74 5.045 5.046 8.176 5.916h3.654c5.568 1.217 11.83.348 17.05 1.913c9.222 2.957 17.572 7.307 25.054 12.005c22.792 14.44 41.58 34.97 54.282 59.501c2.088 4 2.957 7.656 4.871 11.83c3.655 8.526 8.178 17.225 11.83 25.576c3.654 8.176 7.133 16.528 12.353 23.314c2.61 3.652 13.048 5.567 17.746 7.481c3.48 1.565 8.874 2.958 12.005 4.871c5.915 3.652 11.83 7.83 17.398 11.83c2.784 2.088 11.482 6.438 12.005 9.917" />
    <path fill="#00546B"
      d="M58.186 43.022c-2.957 0-5.044.35-7.132.871v.348h.348c1.393 2.784 3.827 4.698 5.566 7.133c1.393 2.783 2.61 5.568 4.003 8.352c.173-.175.347-.348.347-.348c2.437-1.741 3.654-4.524 3.654-8.7c-1.044-1.217-1.218-2.435-2.088-3.653c-1.043-1.741-3.306-2.61-4.698-4.003" />
  </svg>
  <svg v-if="name === 'sqlite'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="71.86" height="32"
    viewBox="0 0 512 228">
    <defs>
      <linearGradient id="logosSqlite0" x1="57.662%" x2="57.662%" y1="2.046%" y2="94.439%">
        <stop offset="0%" stop-color="#97D9F6" />
        <stop offset="92.024%" stop-color="#0F80CC" />
        <stop offset="100%" stop-color="#0F80CC" />
      </linearGradient>
    </defs>
    <path fill="#003B57"
      d="M194.52 112.044c-6.821 0-12.368 2.02-16.62 6.055c-4.251 4.04-6.408 9.335-6.408 15.824c0 3.362.535 6.428 1.59 9.237c1.056 2.815 2.699 5.423 4.907 7.78s6.628 5.561 13.215 9.635c8.084 4.934 13.373 8.939 15.912 12.066c2.54 3.124 3.801 6.398 3.801 9.812c0 4.57-1.504 8.219-4.597 10.961c-3.097 2.744-7.24 4.11-12.375 4.11c-5.417 0-10.136-1.909-14.188-5.7c-4.052-3.798-6.098-8.821-6.144-15.117h-2.52v22.851h2.52c.77-2.164 1.834-3.27 3.227-3.27c.67 0 2.24.461 4.685 1.325c5.949 2.117 10.834 3.138 14.674 3.138c6.617 0 12.266-2.317 16.972-7.027c4.7-4.708 7.072-10.387 7.072-17.017c0-5.14-1.566-9.715-4.64-13.701c-3.075-3.992-9.054-8.635-17.99-13.967c-7.689-4.62-12.68-8.382-14.983-11.315c-2.307-2.929-3.492-6.169-3.492-9.724c0-3.845 1.413-6.934 4.199-9.238c2.786-2.305 6.437-3.447 11.006-3.447c5.14 0 9.426 1.526 12.817 4.597c3.388 3.076 5.347 7.339 5.923 12.817h2.52v-19.8h-2.343c-.287 1.009-.552 1.654-.796 1.944c-.237.288-.693.442-1.37.442c-.815 0-2.268-.343-4.332-1.017c-4.42-1.488-8.495-2.254-12.243-2.254m82.342 0c-8.311 0-15.857 1.96-22.674 5.879c-6.828 3.912-12.233 9.345-16.221 16.265S232 148.456 232 156.288c0 10.52 3.444 19.894 10.387 28.11c6.946 8.21 15.27 13.326 24.928 15.293c2.208 1.15 5.367 4.12 9.503 8.928c4.66 5.425 8.603 9.338 11.845 11.668a35.6 35.6 0 0 0 10.43 5.172c3.7 1.126 7.699 1.68 12.023 1.68c5.237 0 9.925-.911 14.055-2.785l-.928-2.299c-2.397.865-4.954 1.282-7.646 1.282c-3.655 0-7.348-1.205-11.05-3.624c-3.697-2.426-8.32-7.053-13.834-13.879c-2.592-3.27-4.381-5.334-5.393-6.143c10.568-2.064 19.257-7.185 26.034-15.382c6.774-8.192 10.165-17.542 10.165-28.022c0-12.442-4.427-22.9-13.215-31.425c-8.796-8.527-19.612-12.818-32.442-12.818m51.403 0l.133 2.696c5.533 0 8.633 1.63 9.326 4.906c.258 1.173.376 3.337.397 6.453l-.044 59.625q-.07 6.68-1.9 8.53c-1.222 1.225-3.287 1.993-6.276 2.298l-.133 2.697h55.16l1.415-13.525h-2.52c-.72 3.684-2.369 6.324-4.994 7.823c-2.633 1.51-7.288 2.254-14.011 2.254h-5.216c-6.05 0-9.55-2.187-10.475-6.586c-.19-.87-.256-1.803-.265-2.828l.22-60.288c0-4.446.561-7.425 1.725-8.884c1.175-1.453 3.295-2.266 6.364-2.475l-.132-2.696zm-50.52 3.27c9.375 0 17.028 3.693 22.94 11.139c5.91 7.449 8.84 17.658 8.84 30.586c0 12.25-2.972 22.058-8.928 29.436c-5.957 7.376-13.884 11.05-23.735 11.05c-9.464 0-17.139-3.789-23.028-11.403c-5.884-7.615-8.795-17.501-8.795-29.658c0-12.492 2.947-22.492 8.884-29.967c5.933-7.466 13.878-11.182 23.823-11.182m126.852 12.819c-1.346 0-2.371.454-3.138 1.37c-.785.912-1.026 2.017-.752 3.359c.265 1.302 1 2.442 2.166 3.403c1.16.96 2.411 1.459 3.757 1.459c1.301 0 2.293-.499 3.005-1.459c.713-.96.93-2.101.663-3.403c-.274-1.342-.983-2.447-2.077-3.36c-1.107-.915-2.323-1.37-3.624-1.37m36.375 9.149c-2.286 8.794-7.241 13.553-14.85 14.32l.088 2.52h8.884l-.177 29.79c.014 5.093.17 8.484.53 10.21c.876 4.131 3.574 6.232 8.089 6.232q9.8-.002 20.552-11.934l-2.165-1.856c-5.175 5.238-9.75 7.867-13.746 7.867c-2.456 0-3.978-1.412-4.553-4.199c-.157-.677-.22-1.468-.22-2.387l.088-33.723h13.569l-.133-4.023h-13.392v-12.817zm52.464 11.226c-7.59 0-13.763 3.685-18.563 11.006c-4.775 7.333-6.253 15.458-4.376 24.398c1.105 5.236 3.306 9.294 6.674 12.154c3.363 2.86 7.629 4.288 12.73 4.288c4.748 0 11.36-1.203 14.143-3.625c2.79-2.42 5.36-6.342 7.735-11.712l-1.9-1.99c-3.788 6.968-11.43 10.476-17.194 10.476c-7.924 0-12.777-4.348-14.586-12.995a32 32 0 0 1-.53-3.536c9.427-1.492 16.571-4.135 21.392-7.955c4.818-3.823 9.655-7.875 8.752-12.155c-.538-2.544-1.858-4.544-3.89-6.055c-2.058-1.511-7.4-2.299-10.387-2.299m-82.96.31l-16.354 3.757v2.917l5.657-.707c2.74 0 4.353 1.24 4.862 3.712c.171.827.28 1.99.31 3.448l-.178 26.74c-.045 3.7-.456 5.851-1.281 6.497q-1.25.972-6.586.973l-.088 2.519h25.944l-.044-2.52c-3.605 0-5.942-.284-6.983-.84c-1.024-.55-1.73-1.555-2.033-3.093c-.235-1.108-.338-3.018-.354-5.657l.089-37.746zm78.806 4.95c1.579 0 3.104.61 4.64 1.812c1.516 1.198 2.439 2.53 2.741 3.978c1.48 7.11-4.823 12.024-19.006 14.762c-.404-5.183.494-9.89 2.785-14.143c2.274-4.25 5.235-6.409 8.84-6.409" />
    <path fill="#0F80CC"
      d="M157.888 9.952H17.15C7.717 9.952 0 17.67 0 27.102V182.31c0 9.432 7.717 17.15 17.15 17.15h92.693c-1.052-46.122 14.698-135.63 48.045-189.508" />
    <path fill="url(#logosSqlite0)"
      d="M152.775 14.955H17.15c-6.698 0-12.148 5.449-12.148 12.148v143.883c30.716-11.788 76.817-21.96 108.693-21.498c6.406-33.494 25.232-99.134 39.08-134.533" />
    <path fill="#003B57"
      d="M190.715 4.872c-9.639-8.595-21.31-5.143-32.827 5.08c-1.71 1.518-3.416 3.203-5.113 5.003c-19.704 20.903-37.994 59.62-43.676 89.19c2.214 4.489 3.943 10.217 5.081 14.593c.292 1.122.555 2.176.766 3.072c.5 2.122.769 3.497.769 3.497s-.177-.668-.902-2.77c-.138-.403-.292-.843-.474-1.361a16 16 0 0 0-.304-.752c-1.285-2.988-4.84-9.294-6.405-12.04a301 301 0 0 0-3.511 10.983c4.517 8.265 7.27 22.429 7.27 22.429s-.239-.918-1.374-4.122c-1.008-2.833-6.027-11.628-7.216-13.684c-2.034 7.509-2.842 12.578-2.113 13.812c1.415 2.391 2.762 6.518 3.946 11.081c2.673 10.28 4.53 22.796 4.53 22.796s.06.83.162 2.106c-.372 8.633-.149 17.584.52 25.674c.885 10.71 2.552 19.91 4.677 24.834l1.443-.786c-3.12-9.701-4.388-22.414-3.833-37.076c.84-22.41 5.997-49.437 15.526-77.606c16.1-42.523 38.436-76.641 58.879-92.935c-18.633 16.828-43.851 71.297-51.4 91.467c-8.453 22.588-14.443 43.784-18.053 64.092c6.229-19.039 26.368-27.222 26.368-27.222s9.877-12.182 21.42-29.586c-6.914 1.577-18.268 4.277-22.071 5.875c-5.61 2.353-7.121 3.156-7.121 3.156s18.17-11.066 33.76-16.076c21.44-33.768 44.799-81.74 21.276-102.724" />
  </svg>
  <svg v-if="name === 'redis'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="37.24" height="32"
    viewBox="0 0 256 220">
    <path fill="#912626"
      d="M245.97 168.943c-13.662 7.121-84.434 36.22-99.501 44.075s-23.437 7.78-35.34 2.09c-11.902-5.69-87.216-36.112-100.783-42.597C3.566 169.271 0 166.535 0 163.951v-25.876s98.05-21.345 113.879-27.024c15.828-5.679 21.32-5.884 34.79-.95c13.472 4.936 94.018 19.468 107.331 24.344l-.006 25.51c.002 2.558-3.07 5.364-10.024 8.988" />
    <path fill="#C6302B"
      d="M245.965 143.22c-13.661 7.118-84.431 36.218-99.498 44.072c-15.066 7.857-23.436 7.78-35.338 2.09c-11.903-5.686-87.214-36.113-100.78-42.594c-13.566-6.485-13.85-10.948-.524-16.166c13.326-5.22 88.224-34.605 104.055-40.284c15.828-5.677 21.319-5.884 34.789-.948c13.471 4.934 83.819 32.935 97.13 37.81c13.316 4.881 13.827 8.9.166 16.02" />
    <path fill="#912626"
      d="M245.97 127.074c-13.662 7.122-84.434 36.22-99.501 44.078c-15.067 7.853-23.437 7.777-35.34 2.087c-11.903-5.687-87.216-36.112-100.783-42.597C3.566 127.402 0 124.67 0 122.085V96.206s98.05-21.344 113.879-27.023c15.828-5.679 21.32-5.885 34.79-.95C162.142 73.168 242.688 87.697 256 92.574l-.006 25.513c.002 2.557-3.07 5.363-10.024 8.987" />
    <path fill="#C6302B"
      d="M245.965 101.351c-13.661 7.12-84.431 36.218-99.498 44.075c-15.066 7.854-23.436 7.777-35.338 2.087c-11.903-5.686-87.214-36.112-100.78-42.594c-13.566-6.483-13.85-10.947-.524-16.167C23.151 83.535 98.05 54.148 113.88 48.47c15.828-5.678 21.319-5.884 34.789-.949c13.471 4.934 83.819 32.933 97.13 37.81c13.316 4.88 13.827 8.9.166 16.02" />
    <path fill="#912626"
      d="M245.97 83.653c-13.662 7.12-84.434 36.22-99.501 44.078c-15.067 7.854-23.437 7.777-35.34 2.087c-11.903-5.687-87.216-36.113-100.783-42.595C3.566 83.98 0 81.247 0 78.665v-25.88s98.05-21.343 113.879-27.021c15.828-5.68 21.32-5.884 34.79-.95C162.142 29.749 242.688 44.278 256 49.155l-.006 25.512c.002 2.555-3.07 5.361-10.024 8.986" />
    <path fill="#C6302B"
      d="M245.965 57.93c-13.661 7.12-84.431 36.22-99.498 44.074c-15.066 7.854-23.436 7.777-35.338 2.09C99.227 98.404 23.915 67.98 10.35 61.497S-3.5 50.55 9.825 45.331C23.151 40.113 98.05 10.73 113.88 5.05c15.828-5.679 21.319-5.883 34.789-.948s83.819 32.934 97.13 37.811c13.316 4.876 13.827 8.897.166 16.017" />
    <path fill="#FFF"
      d="m159.283 32.757l-22.01 2.285l-4.927 11.856l-7.958-13.23l-25.415-2.284l18.964-6.839l-5.69-10.498l17.755 6.944l16.738-5.48l-4.524 10.855zm-28.251 57.518L89.955 73.238l58.86-9.035zm-56.95-50.928c17.375 0 31.46 5.46 31.46 12.194c0 6.736-14.085 12.195-31.46 12.195s-31.46-5.46-31.46-12.195c0-6.734 14.085-12.194 31.46-12.194" />
    <path fill="#621B1C" d="m185.295 35.998l34.836 13.766l-34.806 13.753z" />
    <path fill="#9A2928" d="m146.755 51.243l38.54-15.245l.03 27.519l-3.779 1.478z" />
  </svg>
  <svg v-if="name === 'fauna'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="28.25" height="32"
    viewBox="0 0 256 290">
    <path fill="#3A1AB6"
      d="M198.883 60.852c-17.581 5.99-26.047 16.735-31.814 33.469c-1.488 4.469-5.209 9.413-9.395 12.741l14.419 15.784l-45.768-32.708L0 0s9.116 60.472 12.279 82.721c2.233 15.688 6.047 22.724 18.14 29.856l4.837 2.662l20.837 11.22l-12.372-6.56l57.116 31.851l-.372.857l-61.488-29.095c3.256 11.41 9.58 33.373 12.279 43.072c2.883 10.458 6.14 14.261 16.093 17.971l18.326 6.845l11.348-4.564l-14.418 9.794l-72.093 93.464c47.906-45.448 88.464-61.612 118.139-74.829c37.86-16.734 60.65-27.478 75.534-66.082c10.605-27.098 18.883-61.802 29.397-75.21L256 44.69s-46.42 12.55-57.117 16.163" />
  </svg>
  <svg v-if="name === 'rest'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="32" height="32"
    viewBox="0 0 256 256">
    <path
      d="M125.371.065c2.83-.088 5.662-.087 8.492.003l53.434 1.692c36.479 1.155 65.779 30.464 66.922 66.943l1.714 54.73c.089 2.83.089 5.66.001 8.49l-1.726 55.363c-1.137 36.484-30.438 65.8-66.921 66.954l-53.424 1.692c-2.83.09-5.662.091-8.492.003l-56.603-1.746c-36.506-1.126-65.84-30.45-66.978-66.956l-1.724-55.31c-.088-2.83-.088-5.66.001-8.49L1.78 68.755c1.143-36.5 30.476-65.819 66.977-66.945L125.371.065z"
      fill="#444F5C" />
    <path
      d="M128 21.582c58.773 0 106.418 47.645 106.418 106.418S186.773 234.418 128 234.418S21.582 186.773 21.582 128S69.227 21.582 128 21.582"
      fill="#313942" />
    <path
      d="M62.937 169.604l23.521-7.856c3.835-1.28 7.485 2.366 6.209 6.202l-7.841 23.57c-1.043 3.137-5.026 4.076-7.361 1.736l-16.266-16.298c-2.331-2.336-1.392-6.308 1.738-7.354"
      fill="#EEB545" />
    <path
      d="M85.326 64.442L92.8 88.01c1.222 3.853-2.479 7.447-6.295 6.114l-23.528-8.222c-3.133-1.095-4.004-5.112-1.606-7.406l16.648-15.922c2.377-2.273 6.313-1.267 7.307 1.868"
      fill="#42AADF" />
    <path
      d="M171.084 191.52l-7.841-23.57c-1.276-3.836 2.375-7.482 6.209-6.202l23.522 7.856c3.13 1.046 4.068 5.018 1.737 7.354l-16.265 16.298c-2.336 2.34-6.318 1.401-7.362-1.737"
      fill="#ED042B" />
    <path
      d="M163.243 88.05l7.841-23.569c1.044-3.138 5.026-4.077 7.362-1.737l16.265 16.298c2.331 2.336 1.393 6.308-1.737 7.354l-23.522 7.856c-3.834 1.28-7.485-2.366-6.209-6.202"
      fill="#2EA757" />
    <g>
      <path
        d="M147.178 146.723c10.341-10.34 10.341-27.106 0-37.446c-10.34-10.34-27.105-10.34-37.446 0c-10.34 10.34-10.34 27.105 0 37.446c10.341 10.34 27.106 10.34 37.446 0zm-32.17-13.346a7.604 7.604 0 0 1 0-10.754l8.07-8.07a7.604 7.604 0 0 1 10.754 0l8.07 8.07a7.604 7.604 0 0 1 0 10.754l-8.07 8.07a7.604 7.604 0 0 1-10.754 0l-8.07-8.07z"
        fill="#596778" />
      <path d="M147.051 114.741a3.88 3.88 0 1 1-7.761-.001a3.88 3.88 0 0 1 7.761.001" fill="#2EA757" />
      <path d="M147.051 141.542a3.88 3.88 0 1 1-7.762 0a3.88 3.88 0 0 1 7.762 0" fill="#ED042B" />
      <path d="M117.824 114.741a3.88 3.88 0 1 1-7.761-.001a3.88 3.88 0 0 1 7.761.001" fill="#42AADF" />
      <path d="M117.824 141.542a3.88 3.88 0 1 1-7.762 0a3.88 3.88 0 0 1 7.762 0" fill="#EEB545" />
    </g>
  </svg>
  <svg v-if="name === 'graphql'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="28.45" height="32"
    viewBox="0 0 256 288">
    <path fill="#E535AB" fill-rule="evenodd"
      d="m152.576 32.963l59.146 34.15a25.8 25.8 0 0 1 5.818-4.604c12.266-7.052 27.912-2.865 35.037 9.402c7.052 12.267 2.865 27.912-9.402 35.037a25.7 25.7 0 0 1-6.831 2.72v68.325a25.7 25.7 0 0 1 6.758 2.702c12.34 7.125 16.527 22.771 9.402 35.038c-7.052 12.266-22.771 16.453-35.038 9.402a25.5 25.5 0 0 1-6.34-5.147l-58.786 33.94a25.7 25.7 0 0 1 1.295 8.08c0 14.103-11.458 25.636-25.635 25.636s-25.635-11.46-25.635-25.636c0-2.52.362-4.954 1.037-7.253l-59.13-34.14a25.8 25.8 0 0 1-5.738 4.52c-12.34 7.051-27.986 2.864-35.038-9.402c-7.051-12.267-2.864-27.913 9.402-35.038a25.7 25.7 0 0 1 6.758-2.703v-68.324a25.7 25.7 0 0 1-6.831-2.72C.558 99.897-3.629 84.178 3.423 71.911s22.77-16.454 35.037-9.402a25.8 25.8 0 0 1 5.79 4.575l59.163-34.159a25.7 25.7 0 0 1-1.048-7.29C102.365 11.46 113.823 0 128 0s25.635 11.459 25.635 25.635c0 2.548-.37 5.007-1.059 7.328m-6.162 10.522l59.287 34.23a25.6 25.6 0 0 0 2.437 19.831c3.609 6.278 9.488 10.44 16.013 12.062v68.41q-.5.122-.993.264L145.725 44.17q.351-.336.689-.684m-36.123.7l-77.432 134.11a26 26 0 0 0-1.01-.27v-68.417c6.525-1.622 12.404-5.784 16.013-12.062a25.6 25.6 0 0 0 2.427-19.869l59.27-34.22q.359.371.732.727m24.872 6.075l77.414 134.08a25.5 25.5 0 0 0-4.513 5.757a25.7 25.7 0 0 0-2.702 6.758H50.64a25.7 25.7 0 0 0-2.704-6.758a25.8 25.8 0 0 0-4.506-5.724l77.429-134.107A25.7 25.7 0 0 0 128 51.27c2.487 0 4.89-.352 7.163-1.01m11.795 194.478l58.902-34.008a26 26 0 0 1-.473-1.682H50.607q-.123.499-.266.992l59.19 34.175A25.56 25.56 0 0 1 128 236.373a25.56 25.56 0 0 1 18.958 8.365" />
  </svg>
  <svg v-if="name === 'serverless'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="40.16" height="32"
    viewBox="0 0 256 204">
    <path fill="#F26D61"
      d="M0 161.202h45.312l-14.039 42.396H0zM0 80.6h72l-14.036 42.396H0zM0 0h98.692l-14.04 42.395H0zm143.349 0H256v42.395H129.312zM116.66 80.6H256v42.397H102.622zm-26.69 80.602H256v42.396H75.933z" />
  </svg>
  <svg v-if="name === 'docker'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="44.29" height="32"
    viewBox="0 0 256 185">
    <path fill="#2396ED"
      d="M250.716 70.497c-5.765-4-18.976-5.5-29.304-3.5c-1.2-10-6.725-18.749-16.333-26.499l-5.524-4l-3.844 5.75c-4.803 7.5-7.205 18-6.485 28c.24 3.499 1.441 9.749 5.044 15.249c-3.362 2-10.328 4.5-19.455 4.5H1.155l-.48 2c-1.682 9.999-1.682 41.248 18.014 65.247c14.892 18.249 36.99 27.499 66.053 27.499c62.93 0 109.528-30.25 131.386-84.997c8.647.25 27.142 0 36.51-18.75c.24-.5.72-1.5 2.401-5.249l.961-2zM139.986 0h-26.42v24.999h26.42zm0 29.999h-26.42v24.999h26.42zm-31.225 0h-26.42v24.999h26.42zm-31.225 0H51.115v24.999h26.421zM46.311 59.998H19.89v24.999h26.42zm31.225 0H51.115v24.999h26.421zm31.225 0h-26.42v24.999h26.42zm31.226 0h-26.422v24.999h26.422zm31.225 0H144.79v24.999h26.422z" />
  </svg>
  <svg v-if="name === 'kube'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="32.9" height="32"
    viewBox="0 0 256 249">
    <path fill="#326DE6"
      d="M82.085 244.934c-5.946 0-11.561-2.642-15.36-7.432L8.92 165.657c-3.799-4.79-5.285-10.9-3.799-16.847l20.645-89.682c1.321-5.946 5.285-10.736 10.736-13.378l83.571-39.97c2.643-1.32 5.616-1.981 8.589-1.981s5.945.66 8.588 1.982l83.572 39.804c5.45 2.642 9.414 7.432 10.735 13.378l20.645 89.682c1.322 5.946 0 12.057-3.798 16.847l-57.807 71.845c-3.799 4.624-9.414 7.432-15.36 7.432z" />
    <path fill="#FFF"
      d="M128.495 7.928c2.313 0 4.625.495 6.772 1.486l83.572 39.804c4.294 2.147 7.597 6.111 8.588 10.736l20.645 89.682c1.156 4.79 0 9.745-3.138 13.543l-57.806 71.846c-2.973 3.798-7.598 5.945-12.387 5.945H82.085c-4.79 0-9.414-2.147-12.387-5.945l-57.806-71.846c-2.973-3.798-4.13-8.753-3.138-13.543l20.645-89.682c1.156-4.79 4.294-8.754 8.588-10.736L121.56 9.25c2.147-.826 4.624-1.321 6.936-1.321m0-7.763c-3.468 0-6.936.826-10.24 2.312l-83.571 39.97c-6.607 3.138-11.231 8.918-12.883 16.02L1.156 148.15c-1.651 7.102 0 14.369 4.625 20.15l57.806 71.845c4.46 5.615 11.231 8.753 18.333 8.753h92.655c7.102 0 13.874-3.138 18.333-8.753l57.807-71.846c4.624-5.615 6.276-13.047 4.624-20.15l-20.645-89.682c-1.651-7.102-6.276-12.882-12.882-16.02L138.57 2.476C135.432.991 131.964.165 128.495.165" />
    <path fill="#FFF"
      d="M212.232 142.534q-.248 0 0 0h-.165c-.165 0-.33 0-.33-.165c-.33 0-.66-.165-.991-.165c-1.156-.165-2.147-.33-3.138-.33c-.496 0-.991 0-1.652-.166h-.165c-3.468-.33-6.276-.66-8.919-1.486c-1.156-.496-1.486-1.156-1.817-1.817c0-.165-.165-.165-.165-.33l-2.147-.66a65.3 65.3 0 0 0-1.156-23.289a68 68 0 0 0-9.249-21.636l1.652-1.486v-.33c0-.826.165-1.652.825-2.478c1.982-1.817 4.46-3.303 7.433-5.12c.495-.33.99-.495 1.486-.826c.991-.495 1.817-.99 2.808-1.651c.165-.165.495-.33.826-.66c.165-.166.33-.166.33-.331c2.312-1.982 2.808-5.285 1.156-7.433c-.826-1.156-2.312-1.816-3.799-1.816c-1.32 0-2.477.495-3.633 1.321l-.33.33c-.33.165-.496.496-.826.661c-.826.826-1.487 1.486-2.147 2.312c-.33.33-.66.826-1.156 1.156c-2.313 2.478-4.46 4.46-6.607 5.946q-.742.495-1.486.496c-.33 0-.661 0-.991-.166h-.33l-1.983 1.322c-2.147-2.312-4.459-4.294-6.771-6.276a65.96 65.96 0 0 0-34.519-13.709l-.165-2.147l-.33-.33c-.496-.496-1.156-.991-1.322-2.147c-.165-2.643.166-5.616.496-8.919v-.165c0-.496.165-1.156.33-1.652c.165-.99.33-1.982.496-3.138v-1.486c0-2.973-2.313-5.45-5.12-5.45c-1.322 0-2.643.66-3.634 1.651c-.99.991-1.486 2.312-1.486 3.799v1.321c0 1.156.165 2.147.495 3.138c.165.496.165.991.33 1.652v.165c.33 3.303.826 6.276.496 8.919c-.165 1.156-.826 1.651-1.321 2.147l-.33.33l-.166 2.147c-2.973.33-5.946.66-8.919 1.321c-12.717 2.808-23.948 9.25-32.701 18.498l-1.652-1.156h-.33c-.33 0-.661.165-.991.165q-.743 0-1.487-.495c-2.147-1.486-4.294-3.634-6.606-6.111c-.33-.33-.66-.826-1.156-1.156c-.661-.826-1.322-1.487-2.148-2.312c-.165-.166-.495-.33-.825-.661c-.165-.165-.33-.165-.33-.33a5.77 5.77 0 0 0-3.634-1.322c-1.487 0-2.973.661-3.799 1.817c-1.652 2.147-1.156 5.45 1.156 7.432c.165 0 .165.166.33.166c.33.165.496.495.826.66c.991.66 1.817 1.156 2.808 1.652c.496.165.991.495 1.487.826c2.972 1.816 5.45 3.303 7.432 5.12c.826.825.826 1.651.826 2.477v.33l1.651 1.487c-.33.495-.66.826-.826 1.321c-8.258 13.048-11.396 28.408-9.249 43.603l-2.147.66c0 .166-.165.166-.165.33c-.33.661-.826 1.322-1.817 1.817c-2.477.826-5.45 1.157-8.918 1.487h-.166c-.495 0-1.156 0-1.651.165c-.991 0-1.982.165-3.138.33c-.33 0-.66.166-.991.166c-.165 0-.33 0-.496.165c-2.973.66-4.79 3.468-4.294 6.11c.496 2.313 2.643 3.8 5.285 3.8c.496 0 .826 0 1.322-.166c.165 0 .33 0 .33-.165c.33 0 .66-.165.99-.165c1.157-.33 1.983-.66 2.974-1.156c.495-.165.99-.496 1.486-.66h.165c3.138-1.157 5.946-2.148 8.589-2.478h.33c.991 0 1.652.495 2.147.826c.165 0 .165.165.33.165l2.313-.33c3.964 12.221 11.561 23.122 21.636 31.05c2.312 1.816 4.624 3.303 7.102 4.79l-.991 2.146c0 .166.165.166.165.33c.33.661.66 1.487.33 2.643c-.99 2.478-2.477 4.955-4.294 7.763v.165c-.33.496-.66.826-.99 1.321c-.661.826-1.157 1.652-1.818 2.643c-.165.165-.33.495-.495.826c0 .165-.165.33-.165.33c-1.321 2.808-.33 5.946 2.147 7.102q.99.496 1.982.496c1.982 0 3.964-1.322 4.955-3.139c0-.165.165-.33.165-.33c.165-.33.33-.66.495-.826c.496-1.156.661-1.982.991-2.973l.496-1.486c1.156-3.303 1.982-5.946 3.468-8.258c.66-.991 1.487-1.156 2.147-1.487c.165 0 .165 0 .33-.165l1.157-2.147c7.267 2.808 15.195 4.294 23.122 4.294c4.79 0 9.745-.495 14.37-1.651a73 73 0 0 0 8.588-2.478l.99 1.817c.166 0 .166 0 .331.165c.826.165 1.486.496 2.147 1.487c1.321 2.312 2.312 5.12 3.468 8.258v.165l.496 1.486c.33.991.495 1.982.99 2.973c.166.33.331.496.496.826c0 .165.166.33.166.33c.99 1.982 2.972 3.139 4.954 3.139q.992 0 1.982-.496c1.156-.66 2.147-1.652 2.478-2.973c.33-1.321.33-2.808-.33-4.129c0-.165-.166-.165-.166-.33c-.165-.33-.33-.66-.495-.826c-.496-.991-1.156-1.817-1.817-2.643c-.33-.495-.66-.825-.99-1.32v-.166c-1.818-2.808-3.47-5.285-4.295-7.763c-.33-1.156 0-1.816.165-2.642c0-.165.165-.165.165-.33l-.826-1.982c8.754-5.12 16.186-12.388 21.802-21.306c2.973-4.625 5.285-9.745 6.936-14.865l1.982.33c.166 0 .166-.165.33-.165c.661-.33 1.157-.825 2.148-.825h.33c2.643.33 5.45 1.32 8.589 2.477h.165c.495.165.99.495 1.486.66c.991.496 1.817.826 2.973 1.157c.33 0 .66.165.991.165c.165 0 .33 0 .495.165c.496.165.826.165 1.322.165c2.477 0 4.624-1.651 5.285-3.798c0-1.982-1.817-4.625-4.79-5.45m-76.47-8.093l-7.267 3.469l-7.267-3.469l-1.816-7.762l4.954-6.276h8.093l4.955 6.276zm43.108-17.176a52.1 52.1 0 0 1 1.156 16.68l-25.27-7.266c-2.312-.66-3.633-2.973-3.138-5.285c.165-.661.496-1.322.991-1.817l19.985-18.003c2.807 4.625 4.954 9.91 6.276 15.69m-14.204-25.6l-21.636 15.36c-1.817 1.156-4.295.825-5.781-.991c-.495-.496-.66-1.157-.826-1.817l-1.486-26.922a50.13 50.13 0 0 1 29.729 14.37M116.769 78.12c1.817-.33 3.468-.66 5.285-.99l-1.486 26.425c-.165 2.312-1.982 4.294-4.46 4.294c-.66 0-1.486-.165-1.982-.495L92.16 91.665c6.772-6.772 15.195-11.397 24.609-13.544m-32.537 23.453l19.654 17.507c1.817 1.487 1.982 4.294.496 6.111c-.496.66-1.156 1.156-1.982 1.322l-25.6 7.432c-.991-11.231 1.486-22.627 7.432-32.372m-4.46 44.759l26.262-4.46c2.147-.165 4.129 1.322 4.624 3.469c.165.99.165 1.817-.165 2.643l-10.075 24.278c-9.249-5.946-16.681-15.03-20.645-25.93m60.285 32.867c-3.799.826-7.598 1.321-11.562 1.321c-5.78 0-11.396-.99-16.68-2.642l13.047-23.618c1.321-1.487 3.468-2.147 5.285-1.156a7 7 0 0 1 1.982 1.816l12.717 22.958c-1.486.495-3.138.826-4.79 1.321m32.206-22.957c-4.129 6.606-9.58 11.891-15.855 16.02l-10.405-24.94c-.496-1.981.33-4.128 2.312-5.12c.66-.33 1.486-.495 2.312-.495l26.426 4.46c-.991 3.633-2.643 6.937-4.79 10.075" />
  </svg>
  <svg v-if="name === 'vite'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="31.88" height="32"
    viewBox="0 0 256 257">
    <defs>
      <linearGradient id="logosVitejs0" x1="-.828%" x2="57.636%" y1="7.652%" y2="78.411%">
        <stop offset="0%" stop-color="#41D1FF" />
        <stop offset="100%" stop-color="#BD34FE" />
      </linearGradient>
      <linearGradient id="logosVitejs1" x1="43.376%" x2="50.316%" y1="2.242%" y2="89.03%">
        <stop offset="0%" stop-color="#FFEA83" />
        <stop offset="8.333%" stop-color="#FFDD35" />
        <stop offset="100%" stop-color="#FFA800" />
      </linearGradient>
    </defs>
    <path fill="url(#logosVitejs0)"
      d="M255.153 37.938L134.897 252.976c-2.483 4.44-8.862 4.466-11.382.048L.875 37.958c-2.746-4.814 1.371-10.646 6.827-9.67l120.385 21.517a6.5 6.5 0 0 0 2.322-.004l117.867-21.483c5.438-.991 9.574 4.796 6.877 9.62" />
    <path fill="url(#logosVitejs1)"
      d="M185.432.063L96.44 17.501a3.27 3.27 0 0 0-2.634 3.014l-5.474 92.456a3.268 3.268 0 0 0 3.997 3.378l24.777-5.718c2.318-.535 4.413 1.507 3.936 3.838l-7.361 36.047c-.495 2.426 1.782 4.5 4.151 3.78l15.304-4.649c2.372-.72 4.652 1.36 4.15 3.788l-11.698 56.621c-.732 3.542 3.979 5.473 5.943 2.437l1.313-2.028l72.516-144.72c1.215-2.423-.88-5.186-3.54-4.672l-25.505 4.922c-2.396.462-4.435-1.77-3.759-4.114l16.646-57.705c.677-2.35-1.37-4.583-3.769-4.113" />
  </svg>
  <svg v-if="name === 'bun'" class="h-full w-full" xmlns="http://www.w3.org/2000/svg" width="36.41" height="32"
    viewBox="0 0 256 225">
    <path
      d="M228.747 65.588a38 38 0 0 0-1.62-1.62c-.55-.519-1.07-1.102-1.62-1.62c-.551-.52-1.07-1.102-1.62-1.62c-.551-.52-1.07-1.103-1.62-1.621c-.552-.519-1.07-1.102-1.62-1.62c-.552-.519-1.07-1.102-1.621-1.62c-.551-.52-1.07-1.102-1.62-1.62a85.74 85.74 0 0 1 25.632 59.819c0 53.695-54.505 97.377-121.519 97.377c-37.525 0-71.097-13.707-93.424-35.192l1.62 1.62l1.62 1.62l1.62 1.62l1.621 1.621l1.62 1.62l1.62 1.62l1.621 1.62c22.295 22.393 56.612 36.813 95.044 36.813c67.014 0 121.519-43.682 121.519-97.215c0-22.878-9.851-44.557-27.253-61.602" />
    <path fill="#FBF0DF"
      d="M234.937 114.066c0 49.288-50.779 89.243-113.418 89.243S8.101 163.354 8.101 114.066c0-30.558 19.443-57.552 49.32-73.56C87.3 24.498 105.9 8.101 121.52 8.101s28.97 13.384 64.097 32.405c29.878 16.008 49.32 43.002 49.32 73.56" />
    <path fill="#F6DECE"
      d="M234.937 114.066a70.2 70.2 0 0 0-2.593-18.73c-8.846 107.909-140.476 113.093-192.227 80.818a129.62 129.62 0 0 0 81.402 27.155c62.542 0 113.418-40.02 113.418-89.243" />
    <path fill="#FFFEFC"
      d="M77.87 34.576c14.484-8.684 33.733-24.984 52.658-25.017a30.1 30.1 0 0 0-9.009-1.458c-7.842 0-16.203 4.05-26.734 10.143c-3.662 2.139-7.453 4.504-11.472 6.967c-7.55 4.666-16.202 9.948-25.924 15.23c-30.85 16.69-49.288 44.201-49.288 73.625v3.856C27.74 48.542 63.417 43.261 77.87 34.576" />
    <path fill="#CCBEA7"
      d="M112.186 16.3a53.18 53.18 0 0 1-18.244 40.409c-.907.81-.194 2.365.972 1.912c10.92-4.245 25.665-16.948 19.443-42.58c-.259-1.459-2.17-1.07-2.17.259m7.356 0a52.63 52.63 0 0 1 5.217 43.65c-.388 1.134 1.005 2.106 1.783 1.166c7.096-9.073 13.286-27.09-5.25-46.534c-.94-.842-2.398.454-1.75 1.588zm8.944-.551a53.2 53.2 0 0 1 22.198 38.108a1.07 1.07 0 0 0 2.106.357c2.981-11.31 1.296-30.59-23.235-40.604c-1.296-.518-2.138 1.232-1.069 2.01zM68.666 49.45a54.9 54.9 0 0 0 33.928-29.164c.584-1.167 2.43-.713 2.14.583c-5.607 25.924-24.37 31.336-36.035 30.623c-1.232.032-1.2-1.685-.033-2.042" />
    <path
      d="M121.519 211.443C54.505 211.443 0 167.761 0 114.066c0-32.405 20.026-62.64 53.566-80.754c9.721-5.184 18.05-10.402 25.47-14.97c4.083-2.528 7.94-4.894 11.666-7.097C102.076 4.505 111.797 0 121.519 0s18.212 3.889 28.84 10.175c3.241 1.847 6.482 3.856 9.949 6.06c8.069 4.99 17.175 10.629 29.164 17.077c33.54 18.115 53.566 48.316 53.566 80.754c0 53.695-54.505 97.377-121.519 97.377m0-203.342c-7.842 0-16.203 4.05-26.734 10.143c-3.662 2.139-7.453 4.504-11.472 6.967c-7.55 4.666-16.202 9.948-25.924 15.23c-30.85 16.69-49.288 44.201-49.288 73.625c0 49.223 50.876 89.276 113.418 89.276s113.418-40.053 113.418-89.276c0-29.424-18.439-56.936-49.32-73.56c-12.25-6.48-21.81-12.573-29.554-17.369c-3.532-2.17-6.773-4.18-9.722-5.962c-9.818-5.833-16.98-9.074-24.822-9.074" />
    <path fill="#B71422"
      d="M144.365 137.722a28.94 28.94 0 0 1-9.463 15.263a22.07 22.07 0 0 1-12.962 6.092a22.17 22.17 0 0 1-13.383-6.092a28.94 28.94 0 0 1-9.333-15.263a2.333 2.333 0 0 1 2.593-2.625h39.988a2.333 2.333 0 0 1 2.56 2.625" />
    <path fill="#FF6164"
      d="M108.557 153.244a22.4 22.4 0 0 0 13.351 6.157a22.4 22.4 0 0 0 13.318-6.157a34.5 34.5 0 0 0 3.241-3.468a22.13 22.13 0 0 0-15.879-7.485a19.93 19.93 0 0 0-16.202 9.008c.745.681 1.393 1.33 2.171 1.945" />
    <path
      d="M109.076 150.684a17.37 17.37 0 0 1 13.577-6.74a19.44 19.44 0 0 1 12.962 5.476a51 51 0 0 0 2.139-2.495a22.68 22.68 0 0 0-15.263-6.254a20.61 20.61 0 0 0-15.846 7.647a31 31 0 0 0 2.43 2.366" />
    <path
      d="M121.81 161.021a24.05 24.05 0 0 1-14.42-6.481a30.85 30.85 0 0 1-10.077-16.365a3.89 3.89 0 0 1 .842-3.24a4.57 4.57 0 0 1 3.662-1.653h39.988a4.67 4.67 0 0 1 3.661 1.653a3.86 3.86 0 0 1 .81 3.24A30.85 30.85 0 0 1 136.2 154.54c-3.93 3.717-9 6-14.388 6.481m-19.993-23.98c-.519 0-.648.227-.68.292a26.86 26.86 0 0 0 8.846 14.16a20.2 20.2 0 0 0 11.828 5.672a20.35 20.35 0 0 0 11.828-5.606a26.9 26.9 0 0 0 8.814-14.161a.68.68 0 0 0-.648-.292z" />
    <g transform="translate(53.792 88.4)">
      <ellipse cx="117.047" cy="40.183" fill="#FEBBD0" rx="18.957" ry="11.147" />
      <ellipse cx="18.957" cy="40.183" fill="#FEBBD0" rx="18.957" ry="11.147" />
      <path
        d="M27.868 35.71a17.855 17.855 0 1 0-17.822-17.854c0 9.848 7.974 17.837 17.822 17.855m80.268 0A17.855 17.855 0 1 0 90.41 17.857c-.018 9.818 7.908 17.801 17.726 17.855" />
      <path fill="#FFF"
        d="M22.36 18.99a6.708 6.708 0 1 0 .064-13.416a6.708 6.708 0 0 0-.065 13.416m80.267 0a6.708 6.708 0 1 0-.065 0z" />
    </g>
  </svg>


  <component
    v-if="name === 'retail' || name === 'hospital' || name === 'education' || name === 'ecommerce' || name === 'social' || name === 'finance' || name === 'sports' || name === 'it' || name === 'travel' || name === 'estate' || name === 'logistics'"
    class="size-24 p-4 text-primary m-auto" :is="iconMap[name]" />
  <!-- <div v-if="name === 'retail'" class="h-full w-full" i-mdi:home-percent /> -->
  <div v-if="name === 'energy'" class="h-full w-full" i-mdi:battery-charging-100 />
  <svg v-if="name === 'mirror'" class="h-full w-full" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M17 8.524L11.415 13.1M17 11.4269L10.6652 17.5259M17 6.28783V22.5595C17 23.2067 16.7893 23.8275 16.4142 24.2852C16.0391 24.7429 15.5304 25 15 25H3C2.46957 25 1.96086 24.7429 1.58579 24.2852C1.21071 23.8275 1 23.2067 1 22.5595V6.28783C1 5.64056 3.83583 2.47892 4.36842 2.22167C5.21053 1.81491 7.73684 1 9.42105 1C10.6842 1 12.7895 1.81319 13.6316 2.22167C14.211 2.50274 17 5.64056 17 6.28783Z"
      stroke="current" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
  </svg>
  <NuxtImg v-if="name === 'aws'" src="/images/icons/aws.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <NuxtImg v-if="name === 'google'" src="/images/icons/google.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <NuxtImg v-if="name === 'vercel'" src="/images/icons/vercel.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <NuxtImg v-if="name === 'netlify'" src="/images/icons/netlify.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <NuxtImg v-if="name === 'cloudflare'" src="/images/icons/cloudflare.svg" loading="lazy" alt="icon"
    class="h-full w-full" />
  <NuxtImg v-if="name === 'coolify'" src="/images/icons/coolify.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <NuxtImg v-if="name === 'thirdweb'" src="/images/icons/thirdweb.svg" loading="lazy" alt="icon"
    class="h-full w-full" />
  <NuxtImg v-if="name === 'spheron'" src="/images/icons/spheron.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <NuxtImg v-if="name === 'graph'" src="/images/icons/graph.svg" loading="lazy" alt="icon" class="h-full w-full" />
  <NuxtImg v-if="name === 'openai'" src="/images/icons/openai.svg" loading="lazy" alt="icon" class="h-full w-full" />
</template>
