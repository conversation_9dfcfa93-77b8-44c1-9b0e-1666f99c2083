<template>
    <div
      :key="name"
      :class="
        cn(
          'group relative col-span-3 flex flex-col justify-end overflow-hidden rounded-xl',
          // dark styles
          'transform-gpu bg-black [border:1px_solid_rgba(255,255,255,.1)] [box-shadow:0_-20px_80px_-20px_#ffffff1f_inset]',
          props.class,
        )
      "
    >
      <slot name="background" />
  
      <div
        class="pointer-events-none z-10 flex transform-gpu flex-col gap-1 p-6 transition-all duration-300 group-hover:-translate-y-10"
      >
        <component
          v-if="icon"
          :is="icon"
          class="h-12 w-12 origin-left transform-gpu text-neutral-700 transition-all duration-300 ease-in-out group-hover:scale-75"
        />
        <div
          v-else
          class="h-12 w-12 origin-left transform-gpu text-neutral-700 transition-all duration-300 ease-in-out group-hover:scale-75"
        ></div>
        <h3 class="text-lg font-medium text-neutral-300">
          {{ name }}
        </h3>
        <p class="max-w-lg text-neutral-400 text-sm">{{ description }}</p>
      </div>
  
      <div
        class="pointer-events-none absolute bottom-0 flex w-full translate-y-10 transform-gpu flex-row items-center p-4 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100"
      >
        <UiButton variant="ghost" asChild size="sm" class="pointer-events-auto">
          <a :href="href"> {{ cta }} → </a>
        </UiButton>
      </div>
      <div
        class="pointer-events-none absolute inset-0 transform-gpu transition-all duration-300 group-hover:bg-neutral-800/10"
      />
    </div>
  </template>
  
  <script lang="ts" setup>
  import { cn } from "@/lib/utils";
  
  const props = defineProps<{
    name: string;
    class: string;
    icon?: string;
    description: string;
    href: string;
    cta: string;
  }>();
  </script>