<template>
    <div
      :class="
        cn(
          'row-span-1 rounded-xl group/bento hover:shadow-none transition duration-200 p-5 backdrop-blur-[50px] bg-[rgb(115,115,115)]/[0.07] shadow-[inset_2px_4px_16px_0px_rgba(248,248,248,0.06)] justify-between flex flex-col space-y-2',
          props.class,
        )
      "
    >
      <slot name="header" />
      <div :class="`group-hover/bento:translate-x-2 transition duration-200 ${contentClass}`">
        <slot name="icon" />
        <div class="font-semibold text-base text-neutral-200 mb-2 mt-2">
          <slot name="title" />
        </div>
        <div class="font-normal text-neutral-300 text-sm">
          <slot name="description" />
        </div>
      </div>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { cn } from "@/lib/utils";
  
  const props = defineProps<{
    class: string;
    contentClass?: string
  }>();
  </script>