<script setup lang="ts">
import { ChevronRight } from 'lucide-vue-next';
import { ref } from 'vue';

const FallingStarsBgComponent = defineAsyncComponent(() =>
  import('./FallingStarsBg.vue')
)
const starsCount = ref(15);
const { width } = useWindowSize();
const particleImg = ref('/images/logo_full_dark.png'); 
if(width.value >=1024){
  particleImg.value = '/images/logo-banner.png';
}
onMounted(()=>{

  if(width.value >=1280){
    starsCount.value = 120;
  }
  else if(width.value >= 1024) {
    starsCount.value = 100;
  }else if(width.value >= 768){
    starsCount.value = 50;
  }
})


</script>

<template>
  <section class="hero-bg">
    <FallingStarsBgComponent class="" :count="starsCount" />
    <div class="relative mx-auto px-4">
      <div class="text-center space-y-8 pt-8">
        <div
          class="max-w-screen-md mx-auto text-center text-3xl sm:text-4xl md:text-5xl font-bold !leading-[2.8rem] md:!leading-[3.5rem] tracking-wider">
          <h1 class="capitalize">
            <span class="text-gradient">Unlock</span> the Power of <span class="text-gradient">Web3</span> <span class="hidden md:block">for Your Business </span>with
            <span class="text-gradient">Dappgenie
            </span>
          </h1>
        </div>
        <p class="hidden md:block max-w-screen-sm mx-auto text-lg text-muted-foreground">
          Your Trusted Partner in Empowering Your Business with Secure and Transparent Blockchain Solutions.
        </p>
        <p class="block md:hidden max-w-screen-sm mx-auto text-lg sm:text-xl text-muted-foreground">
          Empower Your Business with Secure and Transparent Blockchain Solutions.
        </p>
      </div>
    </div>
    <NuxtLink to="#contact" class="contactHeroBtn mt-10 px-10 py-2.5 sm:py-3 rounded-full flex items-center text-md sm:text-lg font-medium relative z-10 bg-gradient-to-r from-[#1a94bd] via-[#006587] to-[#00ddeb] group transition-all duration-200 ease-linear">Get in touch <ChevronRight class="group-hover:translate-x-1 transition-transform w-[20px] ml-1 pt-0.5 h-auto"/></NuxtLink>

    <!-- <div className='relative lg:absolute lg:bottom-[-30px] w-full mt-6 sm:mt-10'>
      <ParticleImage :imageSrc="particleImg" :responsiveWidth="true" />
    </div> -->
  </section>
</template>

<style scoped>
.hero-bg{
  @apply sticky top-0 min-h-screen overflow-hidden flex flex-col justify-center items-center bg-[radial-gradient(circle_at_center,#04232e_0%,#020e12_50%,#000101_100%)];
}
.text-gradient{
  @apply text-transparent bg-gradient-to-r from-[#1a94bd] via-[#006587] to-[#00ddeb] bg-clip-text;
}

  .contactHeroBtn:hover{
    box-shadow: 0px 4px 36px #1a94bd60;
    @apply scale-105;
  }
.img-shadow-animation {
  animation-name: img-shadow-animation;
  animation-iteration-count: infinite;
  animation-duration: 2s;
  animation-timing-function: linear;
  animation-direction: alternate;
}

.img-border-animation {
  animation-name: img-border-animation;
  animation-iteration-count: infinite;
  animation-duration: 2s;
  animation-timing-function: linear;
  animation-direction: alternate;
}

@keyframes img-shadow-animation {
  from {
    opacity: 0.5;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0px);
  }
}

@keyframes img-border-animation {
  from {
    @apply border-t-primary/10;
  }

  to {
    @apply border-t-primary/60;
  }
}
</style>
