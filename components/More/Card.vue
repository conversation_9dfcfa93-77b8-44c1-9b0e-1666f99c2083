<script setup lang="ts">
import { ChevronRight, MousePointerClick } from 'lucide-vue-next';

defineProps({
  title: String,
  description: String,
  href: String
});
</script>

<template>
    <ClientOnly>
        <div class="w-full relative max-w-md">
          <div
            class="absolute inset-0 h-full w-full bg-gradient-to-r from-gray-300 to-white transform scale-[0.80] rounded-full"
          />
    <CardSpotlight
      class="relative p-8 h-full overflow-hidden rounded-xl flex flex-col justify-end items-start"
      gradient-color="#083040"
    >
            <div
              class="h-5 w-5 rounded-full border flex items-center justify-center mb-4"
            >
            <MousePointerClick class="w-5 h-5 text-gray-300" />
            </div>
  
            <h1 class="font-semibold text-lg text-foreground mb-4 relative z-50">
              {{ title }}
            </h1>
  
            <p class="font-normal text-sm text-muted-foreground mb-6 relative z-50">
                {{ description }}
            </p>
  
            <!-- <NuxtLink :href="href" class="hover:!bg-primary py-2 text-white hover:text-primary-foreground border border-white/20 !bg-primary/10 flex justify-center items-center gap-x-2 px-4 rounded-lg text-sm cursor-pointer font-medium">Explore More <ChevronRight class="w-5 h-5" /> </NuxtLink> -->
            <OutlineLink class="px-4 rounded-lg text-sm font-medium !mt-2 !m-auto" :href="href!">Explore More</OutlineLink>
          </CardSpotlight>
        </div>
    </ClientOnly>
  </template>

<!-- <template>
  <div class="flex h-[500px] w-full flex-col gap-4 lg:h-[250px] lg:flex-row">
    </CardSpotlight>
  </div>
</template> -->