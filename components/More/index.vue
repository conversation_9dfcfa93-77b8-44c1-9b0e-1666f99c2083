<script setup lang="ts">
import Card from './Card.vue';
const props = defineProps({
  hide: String
});
const moreItems = [
    {
        title: "About Us",
        description: "Discover our journey in blockchain innovation and meet the team behind DappGenie Technologies",
        href: '/about-us'
    },
    {
        title: "Industries",
        description: "Explore how we transform various sectors with blockchain technology solutions",
        href: '/industries'
    },
    {
        title: "Services",
        description: "Comprehensive blockchain services from DeFi development to smart contract solutions",
        href: '/services'
    },
    {
        title: "Blogs",
        description: "Stay updated with the latest insights in blockchain technology and industry trends",
        href: '/blogs'
    },
    {
        title: "Contact",
        description: "Get in touch with our team to discuss your blockchain project requirements",
        href: '/contact'
    }
];

const filteredItems = computed(() => {
    return moreItems.filter((item) => (item.title !== (props?.hide ?? '')))
})
</script>

<template>
    <section
      id="more"
      class="container py-24 sm:py-32"
    >
      <h2 class="text-lg text-primary text-center mb-2 tracking-wider">
        Know More
      </h2>
    <h2 class="text-3xl md:text-4xl text-center font-bold mb-4">
      Find more about Dappgenie
    </h2>
    <ClientOnly>
        <div class="flex justify-center items-center flex-wrap gap-10 pt-12">
            <Card v-for="{description, title, href} of filteredItems" :description="description" :href="href" :title="title" />
        </div>
    </ClientOnly>
    </section>
  </template>