<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Marquee } from "@selemondev/vue3-marquee";

import {
  Sparkle,
  Tag,
  Paintbrush,
  Blocks,
  LineChart,
  Wallet,
} from "lucide-vue-next";


defineProps<{
  hideGradient?: boolean;
}>()

interface AboutProps {
  icon: string;
  title: string;
  description: string;
}

const aboutList: AboutProps[] = [
  {
    icon: "blocks",
    title: "Blockchain Pioneers",
    description:
      "Industry leaders in blockchain innovation, delivering cutting-edge Web3 solutions, smart contracts, and decentralized applications.",
  },
  {
    icon: "lineChart",
    title: "Client-Centric Approach",
    description:
      "We put your business needs first, ensuring solutions align perfectly with your goals and vision for blockchain success.",
  },
  {
    icon: "wallet",
    title: "Tailored Solutions",
    description:
      "Custom blockchain solutions designed for your specific needs, from DeFi platforms to NFT marketplaces and beyond.",
  },
  {
    icon: "sparkle",
    title: "Innovation & Excellence",
    description:
      "Combining technical expertise with creative problem-solving to deliver secure and innovative blockchain solutions.",
  },
  {
    icon: "sparkle",
    title: "On-Time Delivery",
    description:
      "Meeting deadlines without compromising quality through efficient project management and agile development.",
  },
  {
    icon: "sparkle",
    title: "Reliability & Satisfaction",
    description:
      "Building lasting partnerships through reliable service and proven track record of delivering secure blockchain applications.",
  },
];

const iconMap: Record<
  string,
  | typeof Sparkle
  | typeof Tag
  | typeof Paintbrush
  | typeof Blocks
  | typeof LineChart
  | typeof Wallet
> = {
  sparkle: Sparkle,
  tag: Tag,
  paintbrush: Paintbrush,
  blocks: Blocks,
  lineChart: LineChart,
  wallet: Wallet,
};
</script>

<template>
  <section
    id="about-us"
    class=" py-24 sm:py-32"
    :class="{ 'bg-gradient-to-r from-card via-background to-card' : hideGradient, 'bg-gradient-to-b from-card to-transparent' : !hideGradient }"
  >
  <div class="container">
    
    <h2 class="text-lg text-primary text-center mb-2 tracking-wider">
      About Us
    </h2>

    <h2 class="text-3xl md:text-4xl text-center font-bold mb-4">
      Your Trusted Technology Partner
    </h2>
    <h3 class="w-full sm:w-3/4 lg:w-1/2 mx-auto text-lg text-center text-muted-foreground mb-8">Our mission is to empower businesses with the transparency, security, and efficiency of blockchain technology, enabling them to transform their operations and achieve their goals.
    </h3>

    <div class="mx-auto">
      <Marquee
        class="gap-[3rem]"
        :pauseOnHover="true"
        :fade="true"
        innerClassName="gap-[3rem]"
      >
        <div
          v-for="({ icon, title, description }, index) in aboutList"
          :key="title"
        >
        <Card
          class="transition-all delay-7 group/number w-80 h-72 !p-3"
        >
          <CardHeader class="!pb-2">
            <div class="flex justify-between">
              <component
                class="size-8 mb-6 text-primary"
                :is="iconMap[icon]"
              />

              <span
                class="text-5xl text-muted-foreground/25 font-medium transition-all delay-75 group-hover/number:text-muted-foreground/30"
                >0{{ index + 1 }}</span
              >
            </div>

            <CardTitle>{{ title }}</CardTitle>
          </CardHeader>

          <CardContent class="text-muted-foreground text-base mt-1.5">
            {{ description }}
          </CardContent>
        </Card>
        </div>
      </Marquee>
    </div>
  </div>
  </section>
</template>
