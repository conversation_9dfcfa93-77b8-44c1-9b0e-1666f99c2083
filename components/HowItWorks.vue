<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface HowItWorksProps {
  badgeTitle: string;
  title: string;
  description: string;
  image: string;
}

const HowItWorksList: HowItWorksProps[] = [
  {
    badgeTitle: "Discovery",
    title: "Initial Consultation & Requirements",
    description:
      "We begin with a thorough consultation to understand your business needs, objectives, and challenges. Our team analyzes your requirements and creates a comprehensive project roadmap aligned with your goals.",
    image: "roboto.png",
  },
  {
    badgeTitle: "Planning",
    title: "Strategic Planning & Solution Design",
    description:
      "Our experts develop a detailed project plan, including technology stack selection, architecture design, and milestone definition. We ensure every aspect of your solution is carefully planned for optimal results.",
    image: "gamestation.png",
  },
  {
    badgeTitle: "Development",
    title: "Agile Development & Implementation",
    description:
      "Using agile methodologies, we build your solution with regular sprints and continuous feedback. Our development process ensures high-quality code, thorough testing, and seamless integration of all components.",
    image: "pacheco.png",
  },
  {
    badgeTitle: "Delivery",
    title: "Deployment & Ongoing Support",
    description:
      "We handle the deployment process with careful attention to security and performance. Post-launch, we provide comprehensive support, maintenance, and updates to ensure your solution continues to evolve and succeed.",
    image: "runner.png",
  },
];
</script>

<template>
  <section
    id="how-we-work"
    class="container py-24 sm:py-32"
  >
    <div class="text-center mb-8">
      <h2 class="text-lg text-primary text-center mb-2 tracking-wider">
        How We Work
      </h2>

      <h2 class="text-3xl md:text-4xl text-center font-bold">
        Step-by-Step Process
      </h2>
    </div>

    <div class="lg:w-[80%] mx-auto relative">
      <div
        v-for="(
          { badgeTitle, title, description, image }, index
        ) in HowItWorksList"
        :key="title"
        :class="[
          'flex mb-8 items-center',
          { ' flex-row-reverse': index % 2 !== 0 },
        ]"
      >
        <Card class="!shadow-none h-full bg-transparent border-0 text-center sm:text-left">
          <CardHeader class="px-0 sm:px-2 md:px-6">
            <div class="pb-4">
              <Badge>{{ badgeTitle }}</Badge>
            </div>

            <CardTitle>
              {{ title }}
            </CardTitle>
          </CardHeader>

          <CardContent class="text-muted-foreground w-full md:w-[80%] px-0 sm:px-2 md:px-6 mx-auto sm:m-0">
            {{ description }}
          </CardContent>
        </Card>

        <img
          :src="image"
          :alt="`Image describing ${title} `"
          className="hidden sm:block w-[150px]  md:w-[250px] lg:w-[300px] mx-auto -scale-x-100 "
        />
        <div
          :class="[
            '-z-10 absolute right-0 w-44 h-72  lg:w-64 lg:h-80 rounded-full bg-primary/10 blur-3xl',
            {
              'left-0': index % 2 !== 0,
            },
          ]"
        ></div>
      </div>
    </div>
  </section>
</template>
