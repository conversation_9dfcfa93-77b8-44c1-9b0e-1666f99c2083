<script lang="ts" setup>
import { ref } from "vue";
import { cn } from "@/lib/utils";
import { useColorMode, useThrottleFn } from "@vueuse/core";
import { useRoute } from 'vue-router';

const mode = useColorMode();
mode.value = "dark";

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import {
  Sheet,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

import { Menu } from "lucide-vue-next";

interface RouteProps {
  href: string;
  label: string;
}

interface FeatureProps {
  title: string;
  description: string;
  href: string;
}

const routeList: RouteProps[] = [
  {
    href: "/about-us",
    label: "About",
  },
  {
    href: "/services",
    label: "Services",
  },
  {
    href: "/industries",
    label: "Industries",
  },
  {
    href: "/blogs",
    label: "Blogs",
  },
  {
    href: "/contact",
    label: "Contact",
  },
];

const featureList: FeatureProps[] = [
  {
    title: "Why Choose Us?",
    description: 'Discover the unique advantages that make us your ideal blockchain partner.',
    href: '#why-choose-us'
  },
  {
    title: "How We Work",
    description: 'Learn about our proven development process and methodology.',
    href: '#how-we-work'
  },
  {
    title: "Testimonials",
    description: 'See what our clients say about their success with our solutions.',
    href: '#testimonials'
  },
  {
    title: "Team",
    description: 'Meet our expert developers and blockchain specialists.',
    href: '#team'
  },
  {
    title: "Partners",
    description: 'Explore our strategic alliances and technology partnerships.',
    href: '#partners'
  },
  {
    title: "FAQs",
    description: 'Find quick answers to common questions about our services.',
    href: '#faqs'
  },
];
const isOpen = ref(false);
const isNavUp = ref(false);

const route = useRoute();

onMounted(() => {
  const headerHeight = window.innerHeight - 150; 
  let lastScrollTop = 0;

  const handleScroll = useThrottleFn(() => {
    const scrollTop = window.scrollY;
    if(headerHeight){
    //if scrolling up
    if (scrollTop > lastScrollTop && scrollTop > headerHeight) {
      isNavUp.value = true;
    } else if (scrollTop < lastScrollTop) {
      isNavUp.value = false;
    }
    lastScrollTop = scrollTop;
  }
  }, 300)

  window.addEventListener('scroll', handleScroll);

  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
})

</script>

<template>
  <header
    :class="cn('px-8 fixed w-full z-50 top-0 transition-all duration-300 ease-in-out backdrop-blur-md', isNavUp ? 'top-[-85px]' : 'top-0')">
    <div :class="{
      'container mx-auto z-40 rounded-xl flex justify-between items-center px-5 py-4': true,
    }">

      <NuxtLink href="/">
        <NuxtImg class="h-10 mt-1.5" loading="lazy" src="/images/logo_full_dark.png" alt="logo" />
      </NuxtLink>
      <!-- Mobile -->
      <div class="flex items-center lg:hidden">
        <Sheet v-model:open="isOpen">
          <SheetTrigger as-child>
            <Menu @click="isOpen = true" class="cursor-pointer block lg:hidden" />
          </SheetTrigger>

          <SheetContent side="left" class="flex flex-col justify-between rounded-tr-2xl rounded-br-2xl bg-card border-white/20">
            <div>
              <SheetHeader class="mb-4 ml-4">
                <SheetTitle class="flex items-center justify-center">
                  <NuxtLink href="/">

                    <NuxtImg class="h-10 mt-1.5" loading="lazy" src="/images/logo_full_dark.png" alt="logo" />
                  </NuxtLink>
                </SheetTitle>
              </SheetHeader>

              <div class="flex flex-col gap-3 my-5">
                <Button as-child variant="ghost" class="justify-start text-base">
                  <NuxtLink to="/" class="w-full justify-center">Home</NuxtLink>
                </Button>
                <Button v-for="{ href, label } in routeList" :key="label" as-child variant="ghost"
                  class="justify-start text-base">
                  <NuxtLink @click="isOpen = false" :href="href" class="w-full justify-center">
                    {{ label }}
                  </NuxtLink>
                </Button>
              </div>
            </div>

            <!-- <SheetFooter class="flex-col sm:flex-col justify-start items-start">
              <Separator class="mb-2" />
            </SheetFooter> -->
          </SheetContent>
        </Sheet>
      </div>

      <!-- Desktop -->
      <NavigationMenu class="hidden lg:block">
        <NavigationMenuList>
          <NavigationMenuItem>
            <NavigationMenuTrigger v-if="route.path === '/'" class="bg-transparent hover:bg-transparent text-base">
              Home
            </NavigationMenuTrigger>
            <NavigationMenuContent is="open">
              <div class="w-[600px] p-4">
                <ul class="grid grid-cols-2 gap-2">
                  <li v-for="{ title, description, href } in featureList" :key="title"
                    class="rounded-md text-sm hover:bg-muted p-3">
                    <a class="w-full h-full" :href="href">

                      <p class="mb-1 font-semibold leading-none text-foreground">
                        {{ title }}
                      </p>
                      <p class="line-clamp-2 text-muted-foreground">
                        {{ description }}
                      </p>
                    </a>
                  </li>
                </ul>
              </div>
            </NavigationMenuContent>
          </NavigationMenuItem>

          <NavigationMenuItem v-if="route.path !== '/'">
            <NavigationMenuLink asChild>
              <Button as-child variant="ghost"
                class="justify-start text-base">
                <NuxtLink to="/">Home</NuxtLink>
              </Button>
            </NavigationMenuLink>
          </NavigationMenuItem>

          <NavigationMenuItem>
            <NavigationMenuLink asChild>
              <Button v-for="{ href, label } in routeList" :key="label" as-child variant="ghost"
                class="justify-start text-base">
                <NuxtLink :to="href">{{ label }}</NuxtLink>
              </Button>
            </NavigationMenuLink>
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>
    </div>
  </header>
</template>

<style scoped>
.shadow-light {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.085);
}

.shadow-dark {
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.141);
}
</style>
