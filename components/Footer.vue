<script setup lang="ts">
const FOOTER_ITEMS = [
  {
    title: 'Dappgenie Technologies Pvt. Ltd.',
    width: '[40%]',
    link: '#',
    index: 0,
    imageUrl: '/images/logo_full.webp',
    description: 'Building next-gen blockchain solutions for global enterprises. We transform businesses through innovative technology and digital excellence.',
  },
  {
    title: 'Company',
    width: '[20%]',
    index: 2,
    routes: [
      {
        title: 'About Us',
        link: '/about-us',
      },
      {
        title: 'Why Choose Us',
        link: '/#why-choose-us',
      },
      {
        title: 'Our Team',
        link: '/#team',
      },
      {
        title: 'Blogs',
        link: '/blogs',
      }
    ],
  },
  {
    title: 'Resources',
    width: '[20%]',
    index: 3,
    routes: [
      {
        title: 'Services',
        link: '/services',
      },
      {
        title: 'Industries',
        link: '/industries',
      },
      {
        title: 'FAQs',
        link: '/#faqs',
      },
      {
        title: 'Contact Us',
        link: '/contact',
      }
    ],
  }
];
const CONTACT_US = [
  {
    title: 'Support Email',
    icon: 'mail',
    content: '<EMAIL>',
    link: 'mailto:<EMAIL>',
  },
  {
    title: 'Twitter Handle',
    icon: 'twitter',
    content: '@dappgenie',
    link: 'https://twitter.com/dappgenie',
  },
  {
    title: 'Connect on LinkedIn',
    icon: 'linkedin',
    content: 'Dappgenie.io',
    link: 'https://www.linkedin.com/company/dappgenie',
  },
  // {
  //   title: 'Read Our Mirror',
  //   icon: 'mirror',
  //   content: 'Dappgenie',
  //   link: 'https://mirror.xyz/0xD7F49ED088573a8463699A9d7E60B6576411562e',
  // },
]
</script>

<template>
  <footer class="container footer-div py-24 pb-16 sm:py-32 sm:pb-24">
    <div class="relative mx-auto p-8 sm:p-10 backdrop-blur-[50px] bg-[rgb(115,115,115)]/[0.07] shadow-[inset_2px_4px_16px_0px_rgba(248,248,248,0.06)] rounded-xl">
      <div class="footer-grid">
        <div v-for="item of FOOTER_ITEMS" :key="item.title" class="footer-section"
          :class="[item.index === 0 ? 'w-full md:w-[48%] lg:w-[50%]' : 'w-[45%] sm:w-[32%] md:w-[20%] xl:w-[21%]']"
          data-aos="fade-up" data-aos-duration="1000">

          <NuxtLink href="/" v-if="item.index === 0">
            <NuxtImg class="mb-4 h-9" loading="lazy" src="/images/logo_full_dark.png" alt="logo" />
          </NuxtLink>
          <h1 class="footer-title font-tsukimi">
            {{ item.title }}
          </h1>
          <p v-if="item?.description" class="footer-description">
            {{ item.description }}
          </p>
          <div v-if="item?.routes" class="flex flex-col">
            <a v-for="list of item.routes" :key="list.title" :href="list.link" class="footer-list">
              {{ list.title }}
            </a>
          </div>
        </div>
      </div>
      <div class="relative mt-3 border-t border-white/20 py-3" data-aos="fade-up" data-aos-duration="1000">
        <div class="absolute left-[35%] px-4 py-2 font-semibold -top-5 md:left-[45%] bg-primary/80 text-sm text-background shadow-[inset_2px_4px_16px_0px_rgba(248,248,248,0.06)] rounded-lg">
          <span>Contact Us</span>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
          <a v-for="item of CONTACT_US" :key="item.title" target="_blank" :href="item.link"
            class="mt-8 flex items-center justify-center p-3 text-muted-foreground space-x-3 hover:text-primary">
            <div class="icon">
              <Icons :name="item.icon" />
            </div>
            <div class="min-w-[170px]">
              <div class="mb-1 text-sm font-medium">
                {{ item.title }}
              </div>
              <div class="text-xs">
                {{ item.content }}
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </footer>
</template>
<style scoped>
.footer-div {
  @apply relative h-fit py-20;
  @apply flex flex-col justify-end items-center md:items-start text-left;
}

.footer-section {
  @apply flex flex-col justify-start items-start py-4;
}

.footer-title {
  @apply text-base font-bold text-left mb-2;
}

.footer-description {
  @apply text-sm text-left py-1 max-w-full md:max-w-[95%] lg:max-w-[80%];
}

.footer-list {
  @apply text-sm text-left py-1.5 first:mt-2 cursor-pointer hover:text-primary hover:font-bold;
}

.footer-grid {
  @apply w-full px-5 pb-5;
  @apply flex flex-wrap flex-row justify-between md:justify-start items-start gap-x-8;
}

.icon {
  @apply h-8 w-8;
  animation: shake 3s cubic-bezier(.36, .07, .19, .97) both infinite;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

@keyframes shake {

  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
    transform: rotate(0deg);
    opacity: 1;
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
    transform: rotate(10deg);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
    transform: rotate(-10deg);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
    transform: rotate(0deg);
    opacity: 0.7;
  }
}
</style>
