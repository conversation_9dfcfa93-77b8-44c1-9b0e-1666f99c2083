<script setup lang="ts">
import { industriesList } from '~/lib/constants';


</script>

<template>
  <section
    id="services"
    class="container py-24 sm:py-32"
  >
    <h2 class="text-lg text-primary text-center mb-2 tracking-wider">
      Industries
    </h2>

    <h2 class="text-3xl md:text-4xl text-center font-bold mb-4">
      Empowering Diverse Sectors
    </h2>
    <h3 class="w-full sm:w-3/4 lg:w-1/2 mx-auto text-lg text-center text-muted-foreground mb-8">
      Our solutions empower businesses to unleash their potential, strengthen their market presence, and boost revenue.
    </h3>
    <div class="grid grid-cols-1 gap-4"></div>
  <BentoGrid class="w-full mx-auto md:grid-cols-1">
    <BentoGridItem
      v-for="(item, index) in industriesList"
      :key="index"
      class="col-span-1 !flex-col md:!flex-row  justify-start items-center max-w-md md:max-w-full mx-auto"
      contentClass="w-full md:w-[80%] lg:w-[60%] !p-0 md:!p-16 !mb-2 md:!mb-8 text-center md:text-left"
    >
      <template #header>
        <div class="flex w-full md:w-[30%] h-full">
          <!-- <div class="rounded-md flex flex-1 h-full w-full bg-zinc-800"></div> -->
            <!-- <div
              class="bg-primary/20 p-2 rounded-xl ring-8 ring-primary/10 w-[90%] h-full flex"
            >
            <Icons :name="item.icon" />
            </div> -->
            <NuxtImg :src="item.img"
    loading="lazy" alt="icon" class="h-full w-full object-cover rounded-lg"
  />

        </div>
      </template>

      <template #title>
        <strong class="text-lg">{{ item.title }}</strong>
      </template>

      <template #icon> </template>

      <template #description>
        <p class="text-base">{{ item.description }}</p>
      </template>
    </BentoGridItem>
  </BentoGrid>
  </section>
</template>
