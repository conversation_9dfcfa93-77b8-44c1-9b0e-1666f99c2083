<script setup lang="ts">
import { ChevronRight } from "lucide-vue-next";
import { servicesList } from "~/lib/constants";

</script>

<template>
  <section id="services" class="container py-24 sm:py-32">
    <h2 class="text-lg text-primary text-center mb-2 tracking-wider">
      Our Services
    </h2>

    <h2 class="text-3xl md:text-4xl text-center font-bold mb-4">
      Diverse Services Dappgenie Offers
    </h2>
    <h3 class="w-full sm:w-3/4 lg:w-1/2 mx-auto text-lg text-center text-muted-foreground mb-4">
      At Dappgenie, we offer a comprehensive range of software development services to help businesses harness the power
      of technology and stay ahead of the competition.
    </h3>
    <OutlineLink href="/services">Explore More Services</OutlineLink>

    <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-4"></div>
    <BentoGrid class="w-full mx-auto">
      <BentoGridItem v-for="(item, index) in servicesList.slice(0, 5)" :key="index"
        :class="index === 3 || index === 6 ? 'md:col-span-2' : ''">
        <template #header>
          <div class="flex w-full h-full">
            <!-- <div class="rounded-md flex flex-1 h-full w-full bg-zinc-800"></div> -->
            <!-- <div
              class="bg-primary/20 p-2 rounded-xl ring-8 ring-primary/10 w-full h-full flex"
            >
            <Icons :name="item.icon" />
            </div> -->
            <NuxtImg :src="item.img" loading="lazy" alt="icon" class="max-h-[10.5rem] w-full object-cover rounded-lg" />

          </div>
        </template>

        <template #title>
          <strong>{{ item.title }}</strong>
        </template>

        <template #icon> </template>

        <template #description>
          <p class="line-clamp-2 text-sm">{{ item?.shortDescription }}</p>
        </template>
      </BentoGridItem>
    </BentoGrid>
  </section>
</template>
