<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

import {
  Sparkle,
  Tag,
  Paintbrush,
  Blocks,
  LineChart,
  Wallet,
} from "lucide-vue-next";

interface BenefitsProps {
  icon: string;
  title: string;
  description: string;
}

const benefitList: BenefitsProps[] = [
  {
    icon: "blocks",
    title: 'Blockchain Application Development',
    description:
      "Build scalable dApps and blockchain solutions tailored to your industry needs.",
  },
  {
    icon: "lineChart",
    title: 'Secure Smart Contract Development',
    description:
      "Deploy bulletproof smart contracts with advanced security and audit protocols.",
  },
  {
    icon: "wallet",
    title: 'Blockchain Consulting Services',
    description:
      "Expert guidance to navigate blockchain implementation and maximize ROI.",
  },
  {
    icon: "sparkle",
    title: 'Custom Blockchain Solutions',
    description:
      "Tailor-made blockchain architectures designed for your unique business challenges.",
  },
];

const iconMap: Record<
  string,
  | typeof Sparkle
  | typeof Tag
  | typeof Paintbrush
  | typeof Blocks
  | typeof LineChart
  | typeof Wallet
> = {
  sparkle: Sparkle,
  tag: Tag,
  paintbrush: Paintbrush,
  blocks: Blocks,
  lineChart: LineChart,
  wallet: Wallet,
};
</script>

<template>
  <section
    id="blockchain"
    class="container py-24 sm:py-32"
  >
    <div class="grid lg:grid-cols-2 place-items-center lg:gap-24">
      <div>
        <h2 class="text-lg text-primary mb-2 tracking-wider">Blockchain Services</h2>

        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            Unlock the Power of Blockchain
        </h2>
        <p class="text-lg text-muted-foreground mb-8">
            At Dappgenie Technologies, we are at the forefront of blockchain innovation. Our team of experts is dedicated to harnessing the full potential of blockchain technology to transform businesses across various industries.
        </p>
      </div>

      <div class="grid lg:grid-cols-2 gap-4 w-full">
        <Card
          v-for="({ icon, title, description }, index) in benefitList"
          :key="title"
          class="!p-2"
        >
          <CardHeader class="!pb-2">
            <div class="flex justify-between">
              <component
                class="size-8 mb-6 text-primary"
                :is="iconMap[icon]"
              />

              <span
                class="text-5xl text-muted-foreground/15 font-medium transition-all delay-75 group-hover/number:text-muted-foreground/30"
                >0{{ index + 1 }}</span
              >
            </div>

            <CardTitle class="text-lg">{{ title }}</CardTitle>
          </CardHeader>

          <CardContent class="text-muted-foreground text-sm">
            {{ description }}
          </CardContent>
        </Card>
      </div>
    </div>
  </section>
</template>