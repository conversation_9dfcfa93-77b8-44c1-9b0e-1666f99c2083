<script setup lang="ts">
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";

import {
  Paintbrush,
  MessageCircle,
  TabletSmartphone,
  BadgeCheck,
  Goal,
  PictureInPicture,
  MousePointerClick,
  Newspaper,
} from "lucide-vue-next";

interface FeaturesProps {
  icon: string;
  title: string;
  description: string;
}

const featureList: FeaturesProps[] = [
  {
    title: 'Experience',
    description:
      'Proven track record serving businesses of all sizes across industries, delivering exceptional results for every client.',
    icon: "badgeCheck",
  },
  {
    title: 'Quality',
    description:
      'Leveraging cutting-edge technologies to deliver premium solutions that exceed expectations and drive business growth.',
    icon: "badgeCheck",
  },
  {
    title: 'Communication',
    description:
      'Maintaining transparent dialogue throughout development, ensuring alignment between client vision and project execution.',
    icon: "badgeCheck",
  },
  {
    title: 'Flexibility',
    description:
      'Offering adaptable solutions tailored to your unique project requirements and business goals throughout the journey.',
    icon: "badgeCheck",
  },
  {
    title: 'Support',
    description:
      'Providing comprehensive post-launch maintenance and technical support to ensure long-term success of each project.',
    icon: "badgeCheck",
  },
  {
    title: 'Innovation',
    description:
      'Staying ahead with emerging technologies and best practices to deliver forward-thinking solutions for your business.',
    icon: "badgeCheck",
  },
];

const iconMap: Record<
  string,
  | typeof BadgeCheck
> = {
  badgeCheck: BadgeCheck,
};
</script>

<template>
  <section
    id="why-choose-us"
    class="container py-24 sm:py-32"
  >
    <h2 class="text-lg text-primary text-center mb-2 tracking-wider">
      Why Choose Us
    </h2>

    <h2 class="text-3xl md:text-4xl text-center font-bold mb-4">
      What Makes Us Different
    </h2>

    <h3 class="w-full sm:w-3/4 lg:w-1/2 mx-auto text-lg text-center text-muted-foreground mb-8">
      At Dappgenie, we're passionate about helping businesses achieve their goals through innovative software solutions. Let us help you take your business to the next level. Contact us today to learn more.
    </h3>

    <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
      <div
        v-for="{ icon, title, description } in featureList"
        :key="title"
      >
        <Card class="h-full">
          <CardHeader class="flex justify-center items-center">
            <div
              class="bg-primary/20 p-2 rounded-full ring-8 ring-primary/10 mb-4"
            >
              <component
                :is="iconMap[icon]"
                class="size-6 text-primary"
              />
            </div>

            <CardTitle>
              {{ title }}
            </CardTitle>
          </CardHeader>

          <CardContent class="text-muted-foreground text-center">
            {{ description }}
          </CardContent>
        </Card>
      </div>
    </div>
  </section>
</template>

<style lang="less" scoped></style>
