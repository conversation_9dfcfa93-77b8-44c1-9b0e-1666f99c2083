<script setup lang="ts">
import type { HTMLAttributes } from "vue";
import { cn } from "@/lib/utils";

const props = defineProps<{
  class?: HTMLAttributes["class"];
}>();
</script>

<template>
  <div
    :class="
      cn(
        'rounded-xl group/bento hover:shadow-xl transition duration-200 p-4 backdrop-blur-[50px] bg-[rgb(115,115,115)]/[0.07] shadow-[inset_2px_4px_16px_0px_rgba(248,248,248,0.06)]',
        props.class
      )
    "
  >
    <slot />
  </div>
</template>
