<!-- components/Preloader.vue -->
<script setup lang="ts">
import { LoaderPinwheel } from 'lucide-vue-next'

</script>

<template>
  <div class="preloader">
    <div class="m-auto">
      <h1 data-text="Dappgenie">
        Dappgenie <LoaderPinwheel class="text-[#ffca05] ml-1 animate-spin inline-block" />
      </h1>
    </div>
  </div>
</template>

<style scoped>
.preloader {
  @apply fixed inset-0 flex items-center justify-center bg-black/50 w-screen h-screen backdrop-blur-sm;
  z-index: 100;
}

h1 {
  @apply relative text-xl text-center text-primary;
}

h1:before {
  @apply text-[#ffca05] absolute overflow-hidden whitespace-nowrap max-w-32;
  content: attr(data-text);
  animation: loading 3s linear infinite;
}

@keyframes loading {
  0% {
    @apply max-w-0;
  }
}
</style>