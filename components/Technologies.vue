<script setup lang="ts">
import { Marquee } from "@selemondev/vue3-marquee";
import "@selemondev/vue3-marquee/dist/style.css";
import Icons from "./Icons.vue";

interface TechnologyProps {
  icon: string;
  title: string;
}
const technologies: TechnologyProps[] = [
    {
      title: 'HTML5',
      icon: 'html',
    },
    {
      title: 'CSS3',
      icon: 'css',
    },
    {
      title: 'Tailwind',
      icon: 'tailwind',
    },
    {
      title: 'UnoCss',
      icon: 'unocss',
    },
    {
      title: 'Javascript',
      icon: 'javascript',
    },
    {
      title: 'Typescript',
      icon: 'typescript',
    },
    {
      title: 'Solidity',
      icon: 'solidity',
    },
    {
      title: 'Viem',
      icon: 'viem',
    },
    {
      title: 'Ethers',
      icon: 'ethers',
    },
    {
      title: 'Wagmi',
      icon: 'wagmi',
    },
    {
      title: 'Hardhat',
      icon: 'hardhat',
    },
    {
      title: 'Foundry',
      icon: 'foundry',
    },
    {
      title: 'Vue 3',
      icon: 'vue',
    },
    {
      title: 'Nuxt 3',
      icon: 'nuxt',
    },
    {
      title: 'Angular',
      icon: 'angular',
    },
    {
      title: 'React',
      icon: 'react',
    },
    {
      title: 'QwikJS',
      icon: 'qwik',
    },
    {
      title: 'SolidJS',
      icon: 'solid',
    },
    {
      title: 'PReact',
      icon: 'preact',
    },
    {
      title: 'React Native',
      icon: 'native',
    },
    {
      title: 'Flutter',
      icon: 'flutter',
    },
    {
      title: 'NestJS',
      icon: 'nest',
    },
    {
      title: 'NodeJS',
      icon: 'node',
    },
    {
      title: 'Deno',
      icon: 'deno',
    },
    {
      title: 'MongoDB',
      icon: 'mongo',
    },
    {
      title: 'PostgreSQL',
      icon: 'postgres',
    },
    {
      title: 'MySQL',
      icon: 'mysql',
    },
    {
      title: 'SQLite',
      icon: 'sqlite',
    },
    {
      title: 'Redis',
      icon: 'redis',
    },
    {
      title: 'FaunaDB',
      icon: 'fauna',
    },
    {
      title: 'REST API',
      icon: 'rest',
    },
    {
      title: 'GraphQL',
      icon: 'graphql',
    },
    {
      title: 'Serverless',
      icon: 'serverless',
    },
    {
      title: 'Docker',
      icon: 'docker',
    },
    {
      title: 'Kubernetes',
      icon: 'kube',
    },
    {
      title: 'Vite',
      icon: 'vite',
    },
    {
      title: 'Bun',
      icon: 'bun',
    },
  ]

</script>

<template>
  <section
    id="sponsors"
    class="max-w-[75%] mx-auto py-24 sm:py-32"
  >
    <h2 class="text-lg text-center mb-6">Technologies we Support</h2>
    <h2 class="text-3xl md:text-4xl text-center font-bold mb-6">
      
Our pursuit of Digital Innovation
    </h2>

    <div class="mx-auto">
      <Marquee
        class="gap-[3rem]"
        :pauseOnHover="true"
        :fade="true"
        innerClassName="gap-[3rem]"
      >

        <div class="mr-4 w-max flex flex-wrap items-center justify-start gap-x-4 py-10 lg:mr-8 lg:gap-x-8">
          <div v-for="(item, idx) of technologies" :key="idx">
            <div class="m-auto h-18 w-18 flex items-center rounded-xl bg-white p-3 text-center">
              <div class="m-auto h-10 w-10">
                <Icons :name="item.icon" />
              </div>
            </div>
            <div class="backdrop-blur-[50px] border border-white/20 bg-[rgb(115,115,115)]/[0.07] shadow-[inset_2px_4px_16px_0px_rgba(248,248,248,0.06)] mt-3 rounded-md px-1 py-0.5 text-xs text-foreground text-center">
              {{ item.title }}
            </div>
          </div>
        </div>

        <!-- <div
          v-for="{ icon, title } in technologies"
          :key="title"
        >
              <div class="m-auto h-20 w-48 flex items-center rounded-xl border p-4 bg-white border-gray-50/[.1 p-3 text-center">
                <div class="m-auto w-full text-gray-500 text-xl font-semibold h-10 flex justify-between items-center space-x-2">
                  <Icons :name="icon" />
                  {{ title }}
                </div>
              </div>
        </div> -->
      </Marquee>
    </div>
  </section>
</template>
