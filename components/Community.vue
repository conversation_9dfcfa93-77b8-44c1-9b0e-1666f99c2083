<script setup lang="ts">
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";

import { But<PERSON> } from "@/components/ui/button";

import { MessageCircleQuestion } from "lucide-vue-next";
</script>

<template>
  <section
    id="community"
    class="bg-[#003143]/20 my-24 sm:my-32"
  >
    <!-- <hr /> -->
    <div class="container py-16">
      <div class="lg:w-[60%] mx-auto">
        <Card
          class="!shadow-none text-center flex flex-col items-center justify-center border-0 px-0 sm:px-4 bg-transparent backdrop-blur-none"
        >
          <CardHeader>
            <CardTitle class="text-4xl md:text-5xl font-semibold !leading-[3.8rem]">
              <Component
                class="w-20 h-20 m-auto mb-4"
                :is="MessageCircleQuestion"
              />
              Do you have any question to
              <span class="text-gradient font-bold">
                Dappgenie?
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent class="lg:w-[80%] text-lg text-muted-foreground">
            We are always here to help. Whether you have a question, a project idea, or just want to learn more about what we do, we would love to hear from you! 🚀
          </CardContent>

          <CardFooter>
            <Button as-child>
              <a
                href="/contact"
                target="_blank"
              >
                Contact The Team
              </a>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
    <!-- <hr /> -->
  </section>
</template>


<style scoped>
.text-gradient{
  @apply text-transparent bg-gradient-to-r from-[#1a94bd] via-[#006587] to-[#00ddeb] bg-clip-text;
}</style>