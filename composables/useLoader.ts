// composables/useLoader.ts
export function useLoader() {
    const cookie = useCookie('hasVisited', {
      maxAge: 60 * 60 * 24 * 30, // 30 days
      path: '/',
      secure: true
    })
    
    const loader = useState('loader', () => true)
    const hasVisited = computed(() => <PERSON><PERSON><PERSON>(cookie.value))
    
    const hideLoader = () => {
      loader.value = false
      cookie.value = 'true'
    }
    
    return {
      loader,
      hideLoader,
      hasVisited
    }
  }