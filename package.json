{"name": "dappgenie-website", "private": true, "type": "module", "scripts": {"dev": "nuxt dev", "preview": "nuxt preview", "generate": "nuxt generate", "build": "nuxt build", "postinstall": "nuxt prepare"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@nuxt/content": "^2.13.4", "@nuxt/image": "^1.10.0", "@nuxtjs/robots": "^4.1.11", "@nuxtjs/sitemap": "^6.1.5", "@nuxtjs/tailwindcss": "^6.14.0", "@radix-icons/vue": "^1.0.0", "@selemondev/vue3-marquee": "^0.0.8", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^11.3.0", "@vueuse/nuxt": "^11.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.3", "embla-carousel": "^8.6.0", "embla-carousel-vue": "^8.6.0", "lucide-vue-next": "^0.452.0", "nuxt": "^3.17.2", "radix-vue": "^1.9.17", "shadcn-nuxt": "^0.10.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vee-validate": "^4.15.0", "vue": "latest", "vue-router": "latest", "vue-use-spring": "^0.3.3", "zod": "^3.24.4"}, "devDependencies": {"@types/node": "^20.17.32", "@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.21", "shiki": "^1.29.2", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}}