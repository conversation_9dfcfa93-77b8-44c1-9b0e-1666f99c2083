<script setup lang="ts">
import DotBackground from '~/components/DotBackground.vue';
import Technologies from '~/components/Technologies.vue';
useSeoMeta({
  title: 'Blockchain Development Services | Dappgenie',
  ogTitle: 'Blockchain Development Services | Dappgenie',
  description: 'Comprehensive blockchain services including smart contracts, DeFi solutions, and dApp development.',
  ogDescription: 'Comprehensive blockchain services including smart contracts, DeFi solutions, and dApp development.',
  ogImage: '/og-image.png',
})
</script>

<template>
  <div>
    <Suspense>
      <ClientOnly>
  <Navbar />
  <DotBackground :animate="true" :direction="'top-right'">
    <p
      className="text-3xl sm:text-4xl md:text-5xl font-bold relative z-20 bg-clip-text text-transparent bg-gradient-to-b from-neutral-200 to-neutral-500 py-8"
    >
      Services We Offer
    </p>
  </DotBackground>
  <ServicesMain/>
  <ServicesBlockchain/>
  <HowItWorks/>
  <Partners />
  <More hide="Services" />
  <Testimonials />
  <Technologies />
  <FAQ />
  <Footer />

      </ClientOnly>
      <template #fallback>
        <div italic op50>
          <span animate-pulse>Loading...</span>
        </div>
      </template>
    </Suspense>
  </div>
</template>