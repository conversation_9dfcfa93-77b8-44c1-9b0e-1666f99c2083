<script setup lang="ts">
import DotBackground from '~/components/DotBackground.vue';
import Technologies from '~/components/Technologies.vue';
useSeoMeta({
  title: 'About Dappgenie | Blockchain Technology Experts',
  ogTitle: 'About Dappgenie | Blockchain Technology Experts',
  description: 'Expert blockchain development team delivering innovative solutions. Learn about our mission and values.',
  ogDescription: 'Expert blockchain development team delivering innovative solutions. Learn about our mission and values.',
  ogImage: '/og-image.png',
})
</script>

<template>
  <div>
    <Suspense>
      <ClientOnly>
  <Navbar />
  <DotBackground :animate="true" :direction="'top-right'">
    <p
      className="text-3xl sm:text-4xl md:text-5xl font-bold relative z-20 bg-clip-text text-transparent bg-gradient-to-b from-neutral-200 to-neutral-500 py-8"
    >
      About Dappgenie
    </p>
  </DotBackground>
  <AboutUs :hideGradient="true"/>
  <HowItWorks/>
  <More hide="About Us" />
  <Team/>
  <Partners />
  <Testimonials />
  <Technologies />
  <FAQ />
  <Footer />

      </ClientOnly>
      <template #fallback>
        <div italic op50>
          <span animate-pulse>Loading...</span>
        </div>
      </template>
    </Suspense>
  </div>
</template>