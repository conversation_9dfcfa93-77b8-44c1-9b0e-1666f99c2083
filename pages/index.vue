<script setup lang="ts">
const { loader, hasVisited } = useLoader()

useSeoMeta({
  title: '<PERSON><PERSON><PERSON><PERSON> | Leading Blockchain and AI Development Company',
  ogTitle: 'Dappgenie | Leading Blockchain and AI Development Company',
  description: 'Dappgenie delivers enterprise-grade blockchain solutions, smart contracts, and Web3 applications.',
  ogDescription: '<PERSON><PERSON><PERSON><PERSON> delivers enterprise-grade blockchain solutions, smart contracts, and Web3 applications.',
  ogImage: '/og-image.png',
})

// Add Schema Markup
useHead({
  script: [
    {
      type: 'application/ld+json',
      children: JSON.stringify({
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Dappgenie Technologies",
        "url": "https://dappgenie.io",
        "logo": "https://dappgenie.io/logo.png",
        "contactPoint": {
          "@type": "ContactPoint",
          "email": "<EMAIL>",
          "contactType": "customer service"
        }
      })
    }
  ]
})

const NavbarEl = defineAsyncComponent(() => 
  import('../components/Navbar.vue')
)
const HeroEl = defineAsyncComponent(() => 
  import('../components/Hero.vue')
)
const AboutUsEl = defineAsyncComponent(() => 
  import('../components/AboutUs/index.vue')
)
const ServicesEl = defineAsyncComponent(() => 
  import('../components/Services/index.vue')
)
const ServicesBlockchainEl = defineAsyncComponent(() => 
  import('../components/Services/Blockchain.vue')
)
const TextScrollDivEl = defineAsyncComponent(() => 
  import('../components/TextScroll/Div.vue')
)
const FeaturesEl = defineAsyncComponent(() => 
  import('../components/Features.vue')
)
const HowItWorksEl = defineAsyncComponent(() => 
  import('../components/HowItWorks.vue')
)
const PartnersEl = defineAsyncComponent(() => 
  import('../components/Partners.vue')
)
const IndustriesEl = defineAsyncComponent(() => 
  import('../components/Industries/index.vue')
)
const CommunityEl = defineAsyncComponent(() => 
  import('../components/Community.vue')
)
const TechnologiesEl = defineAsyncComponent(() => 
  import('../components/Technologies.vue')
)
const TestimonialsEl = defineAsyncComponent(() => 
  import('../components/Testimonials/index.vue')
)
const TeamEl = defineAsyncComponent(() => 
  import('../components/Team.vue')
)
const ContactEl = defineAsyncComponent(() => 
  import('../components/Contact.vue')
)
const FAQEl = defineAsyncComponent(() => 
  import('../components/FAQ.vue')
)
const FooterEl = defineAsyncComponent(() => 
  import('../components/Footer.vue')
)
</script>

<template>
  <!-- <Loader  /> -->
  <!-- <PreLoader v-if="loader && !hasVisited" /> -->
  <PreLoader v-if="(!hasVisited) && loader" />
  <Loader v-else-if="loader" />

  <div>
    <Suspense>
      <ClientOnly>
        <NavbarEl  />
        <HeroEl  />
        <div class="relative bg-gradient-to-r from-card via-background to-card">
        <AboutUsEl  />
        <ServicesEl  />
        <ServicesBlockchainEl  />
        <TextScrollDivEl  />
        <FeaturesEl  />
        <HowItWorksEl  />
        <PartnersEl  />
        <IndustriesEl  />
        <CommunityEl  />
        <TechnologiesEl  />
        <TestimonialsEl  />
        <TeamEl  />
        <!-- <PricingEl  /> -->
        <ContactEl  />
        <FAQEl  />
        <FooterEl  />
        </div>

      </ClientOnly>
      <template #fallback>
        <div italic op50>
          <Loader class="animate-pulse w-6 h-6" />
        </div>
      </template>
    </Suspense>
  </div>
</template>