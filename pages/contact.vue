<script setup lang="ts">
import Contact from '~/components/Contact.vue';
import DotBackground from '~/components/DotBackground.vue';
import Technologies from '~/components/Technologies.vue';
useSeoMeta({
  title: 'Contact Dappgenie | Blockchain Development Services',
  ogTitle: 'Contact Dappgenie | Blockchain Development Services',
  description: 'Get in touch with <PERSON><PERSON><PERSON><PERSON> for blockchain development services. Connect with our experts.',
  ogDescription: 'Get in touch with <PERSON><PERSON><PERSON><PERSON> for blockchain development services. Connect with our experts.',
  ogImage: '/og-image.png',
})
</script>

<template>
  <div>
    <Suspense>
      <ClientOnly>
  <Navbar />
  <DotBackground :animate="true" :direction="'top-right'">
    <p
      className="text-3xl sm:text-4xl md:text-5xl font-bold relative z-20 bg-clip-text text-transparent bg-gradient-to-b from-neutral-200 to-neutral-500 py-8"
    >
      Contact Dappgenie
    </p>
  </DotBackground>
  <Contact />
  <Partners />
  <More hide="Contact" />
  <Testimonials />
  <Technologies />
  <FAQ />
  <Footer />

      </ClientOnly>
      <template #fallback>
        <div italic op50>
          <span animate-pulse>Loading...</span>
        </div>
      </template>
    </Suspense>
  </div>
</template>