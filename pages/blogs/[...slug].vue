<!-- pages/blogs/[...slug].vue -->
<script setup lang="ts">
const route = useRoute();
const router = useRouter();
const { data: blog } = await useAsyncData(route.path, () => queryContent(route.path).findOne())

// SEO
useSeoMeta({
  title: blog.value?.title,
  description: blog.value?.description,
  ogImage: blog.value?.image?.src,
  twitterCard: "summary_large_image",
  articleAuthor: blog.value?.authors?.[0]?.name,
})

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatCategory = (category: string) => {
  return category.split('-').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

const goBack = () => {
  router.push('/blogs')
}
</script>

<template>
  <main class="min-h-screen bg-background">
    <!-- Back Button -->
    <div class="container mx-auto px-4 py-4">
      <button 
        @click="goBack"
        class="inline-flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors group"
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="20" 
          height="20" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-linejoin="round"
          class="group-hover:-translate-x-1 transition-transform"
        >
          <path d="m12 19-7-7 7-7"/>
          <path d="M19 12H5"/>
        </svg>
        Back to Blogs
      </button>
    </div>

    <!-- Hero Section -->
    <div v-if="blog" class="relative h-[60vh] min-h-[400px] w-full">
      <ContentRenderer :value="blog">
      <img 
        :src="blog.image?.src" 
        :alt="blog.image?.alt"
        class="absolute inset-0 w-full h-full object-cover"
      >
      <div class="absolute inset-0 bg-gradient-to-t from-background to-background/60">
        <div class="container mx-auto px-8 h-full flex items-end pb-16">
          <div class="max-w-3xl">
            <div class="flex gap-2 mb-4 flex-wrap">
              <span 
                v-for="category in blog.categories" 
                :key="category"
                class="text-sm font-medium bg-primary/90 text-primary-foreground px-3 py-1 rounded-full"
              >
                {{ formatCategory(category) }}
              </span>
            </div>
            <h1 class="text-3xl md:text-4xl font-bold mb-4 text-foreground">
              {{ blog.title }}
            </h1>
            <p class="text-lg text-muted-foreground mb-6">
              {{ blog.description }}
            </p>
            <div class="flex items-center gap-4 text-muted-foreground">
              <div class="flex items-center gap-2">
                <div class="w-10 h-10 rounded-full overflow-hidden bg-muted flex items-center justify-center p-1.5">
                  <img 
                    v-if="blog.authors?.[0]?.avatar"
                    :src="blog.authors[0].avatar" 
                    :alt="blog.authors[0].name"
                    class="w-full h-full object-contain"
                  >
                  <span v-else class="text-sm">
                    {{ blog.authors?.[0]?.name?.charAt(0) }}
                  </span>
                </div>
                <span class="font-medium">{{ blog.authors?.[0]?.name }}</span>
              </div>
              <time class="text-sm">{{ formatDate(blog.publishedAt) }}</time>
            </div>
          </div>
        </div>
      </div>
      </ContentRenderer>
    </div>

    <!-- Content -->
    <div class="container mx-auto px-4 py-16">
      <div class="max-w-3xl mx-auto">
        <article class="prose prose-lg max-w-none">
          <!-- <ContentRenderer :value="blog" /> -->
      <ContentRendererMarkdown :value="blog" />
        </article>
      </div>
    </div>
  </main>
</template>

<style scoped>
/* Base prose styles */
.prose {
  @apply max-w-none text-foreground;
}

/* Headings */
.prose h1 {
  @apply text-4xl font-bold mb-8 text-foreground scroll-m-20;
}

.prose h2 {
  @apply text-2xl font-bold mt-12 mb-6 text-foreground scroll-m-20;
}

.prose h3 {
  @apply text-xl font-semibold mt-8 mb-4 text-foreground scroll-m-20;
}

/* Paragraphs and text */
.prose p {
  @apply text-base leading-7 mb-6 text-foreground;
}

.prose strong {
  @apply font-semibold text-foreground;
}

.prose em {
  @apply italic text-foreground;
}

/* Lists */
.prose ul {
  @apply list-disc list-inside my-6 space-y-2 text-foreground;
}

.prose ol {
  @apply list-decimal list-inside my-6 space-y-2 text-foreground;
}

.prose li {
  @apply text-foreground;
}

/* Blockquotes */
.prose blockquote {
  @apply border-l-4 border-primary pl-6 italic my-8 text-foreground;
}

.prose blockquote p {
  @apply text-muted-foreground;
}

/* Code blocks */
.prose code {
  @apply bg-muted px-1.5 py-0.5 rounded text-sm font-mono text-foreground;
}

.prose pre {
  @apply bg-muted p-4 rounded-lg my-6 overflow-x-auto;
}

.prose pre code {
  @apply bg-transparent p-0 text-sm leading-loose;
}

/* Links */
.prose a {
  @apply text-primary hover:underline transition-colors duration-200;
}

/* Images */
.prose img {
  @apply rounded-lg my-8 ring-1 ring-border mx-auto;
}

/* Tables */
.prose table {
  @apply w-full my-8 border-collapse text-sm;
}

.prose thead {
  @apply bg-muted;
}

.prose th {
  @apply p-4 text-left font-semibold border border-border;
}

.prose td {
  @apply p-4 border border-border;
}

/* Alert components */
.prose .alert {
  @apply p-4 rounded-lg my-6;
}

.prose .alert-info {
  @apply bg-primary/10 text-primary;
}

.prose .alert-success {
  @apply bg-green-500/10 text-green-500;
}

/* Custom components */
.prose :where(code):not(:where([class~="not-prose"] *)) {
  @apply before:content-[''] after:content-[''];
}

.prose hr {
  @apply my-8 border-border;
}

/* Adjustments for dark mode */
/* .dark .prose {
  @apply prose-invert;
} */

/* Mobile responsiveness */
@media (max-width: 768px) {
  .prose {
    @apply text-sm;
  }
  
  .prose h1 {
    @apply text-3xl;
  }
  
  .prose h2 {
    @apply text-xl;
  }
  
  .prose h3 {
    @apply text-lg;
  }
}
</style>