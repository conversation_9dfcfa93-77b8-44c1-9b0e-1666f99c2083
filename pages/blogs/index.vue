<script setup lang="ts">
import DotBackground from '~/components/DotBackground.vue';
import Technologies from '~/components/Technologies.vue';
</script>

<template>
  <div>
    <Suspense>
      <ClientOnly>
  <Navbar />
  <DotBackground :animate="true" :direction="'top-right'">
    <p
      className="text-3xl sm:text-4xl md:text-5xl font-bold relative z-20 bg-clip-text text-transparent bg-gradient-to-b from-neutral-200 to-neutral-500 py-8"
    >
      Dappgenie Blogs
    </p>
  </DotBackground>
  <Blogs/>
  <Partners />
  <More hide="Blogs" />
  <Testimonials />
  <Technologies />
  <FAQ />
  <Footer />

      </ClientOnly>
      <template #fallback>
        <div italic op50>
          <span animate-pulse>Loading...</span>
        </div>
      </template>
    </Suspense>
  </div>
</template>